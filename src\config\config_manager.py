"""
Configuration Manager for YOLO Camera System
Handles loading, saving, and managing application configurations.
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger


class ConfigManager:
    """Configuration manager for the YOLO Camera System."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the configuration file. If None, uses default.
        """
        self.project_root = Path(__file__).parent.parent.parent
        self.config_dir = self.project_root / "config"
        self.config_dir.mkdir(exist_ok=True)
        
        if config_path is None:
            self.config_path = self.config_dir / "default_config.yaml"
        else:
            self.config_path = Path(config_path)
            
        self.user_config_path = self.config_dir / "user_config.yaml"
        self._config = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from file."""
        try:
            # Load default configuration
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f) or {}
                logger.info(f"Loaded default configuration from {self.config_path}")
            else:
                logger.warning(f"Default config file not found: {self.config_path}")
                self._config = {}
            
            # Load user configuration and merge
            if self.user_config_path.exists():
                with open(self.user_config_path, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f) or {}
                self._merge_config(user_config)
                logger.info(f"Loaded user configuration from {self.user_config_path}")
                
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self._config = {}
    
    def _merge_config(self, user_config: Dict[str, Any]) -> None:
        """
        Merge user configuration with default configuration.
        
        Args:
            user_config: User configuration dictionary
        """
        def merge_dict(base: Dict, override: Dict) -> Dict:
            """Recursively merge dictionaries."""
            result = base.copy()
            for key, value in override.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        self._config = merge_dict(self._config, user_config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key.
        
        Args:
            key: Configuration key (supports dot notation, e.g., 'camera.frame_rate')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value by key.
        
        Args:
            key: Configuration key (supports dot notation)
            value: Value to set
        """
        keys = key.split('.')
        config = self._config
        
        # Navigate to the parent dictionary
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
        logger.debug(f"Set configuration: {key} = {value}")
    
    def save_user_config(self) -> None:
        """Save current configuration to user config file."""
        try:
            with open(self.user_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"Saved user configuration to {self.user_config_path}")
        except Exception as e:
            logger.error(f"Error saving user configuration: {e}")
    
    def reset_to_default(self) -> None:
        """Reset configuration to default values."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f) or {}
                logger.info("Configuration reset to default values")
            else:
                self._config = {}
                logger.warning("No default configuration file found")
        except Exception as e:
            logger.error(f"Error resetting configuration: {e}")
    
    def export_config(self, export_path: str, format: str = 'yaml') -> None:
        """
        Export current configuration to file.
        
        Args:
            export_path: Path to export file
            format: Export format ('yaml' or 'json')
        """
        try:
            export_path = Path(export_path)
            export_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format.lower() == 'yaml':
                with open(export_path, 'w', encoding='utf-8') as f:
                    yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
            elif format.lower() == 'json':
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(self._config, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"Unsupported format: {format}")
                
            logger.info(f"Configuration exported to {export_path}")
        except Exception as e:
            logger.error(f"Error exporting configuration: {e}")
    
    def import_config(self, import_path: str) -> None:
        """
        Import configuration from file.
        
        Args:
            import_path: Path to import file
        """
        try:
            import_path = Path(import_path)
            if not import_path.exists():
                raise FileNotFoundError(f"Configuration file not found: {import_path}")
            
            with open(import_path, 'r', encoding='utf-8') as f:
                if import_path.suffix.lower() in ['.yaml', '.yml']:
                    imported_config = yaml.safe_load(f)
                elif import_path.suffix.lower() == '.json':
                    imported_config = json.load(f)
                else:
                    raise ValueError(f"Unsupported file format: {import_path.suffix}")
            
            self._config = imported_config or {}
            logger.info(f"Configuration imported from {import_path}")
        except Exception as e:
            logger.error(f"Error importing configuration: {e}")
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration as dictionary."""
        return self._config.copy()
    
    def validate_config(self) -> bool:
        """
        Validate current configuration.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Basic validation checks
            required_sections = ['app', 'camera', 'yolo', 'ui']
            for section in required_sections:
                if section not in self._config:
                    logger.error(f"Missing required configuration section: {section}")
                    return False
            
            # Validate specific values
            if self.get('camera.frame_rate', 0) <= 0:
                logger.error("Invalid camera frame rate")
                return False
            
            if self.get('yolo.confidence_threshold', 0) < 0 or self.get('yolo.confidence_threshold', 1) > 1:
                logger.error("Invalid YOLO confidence threshold")
                return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return False


# Global configuration instance
config_manager = ConfigManager()

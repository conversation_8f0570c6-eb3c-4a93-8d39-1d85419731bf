"""
Common utility functions for YOLO Camera System
"""

import os
import time
import json
import pickle
import hashlib
import threading
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import numpy as np
import cv2
from loguru import logger


def ensure_dir(path: Union[str, Path]) -> Path:
    """
    Ensure directory exists, create if it doesn't.
    
    Args:
        path: Directory path
        
    Returns:
        Path object
    """
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_timestamp(format_str: str = "%Y%m%d_%H%M%S") -> str:
    """
    Get current timestamp as string.
    
    Args:
        format_str: Timestamp format string
        
    Returns:
        Formatted timestamp string
    """
    return datetime.now().strftime(format_str)


def get_file_hash(file_path: Union[str, Path], algorithm: str = "md5") -> str:
    """
    Calculate file hash.
    
    Args:
        file_path: Path to file
        algorithm: Hash algorithm (md5, sha1, sha256)
        
    Returns:
        File hash as hex string
    """
    hash_func = hashlib.new(algorithm)
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_func.update(chunk)
    
    return hash_func.hexdigest()


def save_json(data: Any, file_path: Union[str, Path], indent: int = 2) -> None:
    """
    Save data to JSON file.
    
    Args:
        data: Data to save
        file_path: Output file path
        indent: JSON indentation
    """
    file_path = Path(file_path)
    ensure_dir(file_path.parent)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=indent, ensure_ascii=False, default=str)


def load_json(file_path: Union[str, Path]) -> Any:
    """
    Load data from JSON file.
    
    Args:
        file_path: Input file path
        
    Returns:
        Loaded data
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def save_pickle(data: Any, file_path: Union[str, Path]) -> None:
    """
    Save data to pickle file.
    
    Args:
        data: Data to save
        file_path: Output file path
    """
    file_path = Path(file_path)
    ensure_dir(file_path.parent)
    
    with open(file_path, 'wb') as f:
        pickle.dump(data, f)


def load_pickle(file_path: Union[str, Path]) -> Any:
    """
    Load data from pickle file.
    
    Args:
        file_path: Input file path
        
    Returns:
        Loaded data
    """
    with open(file_path, 'rb') as f:
        return pickle.load(f)


def resize_image(
    image: np.ndarray,
    target_size: Tuple[int, int],
    keep_aspect_ratio: bool = True,
    interpolation: int = cv2.INTER_LINEAR
) -> np.ndarray:
    """
    Resize image to target size.
    
    Args:
        image: Input image
        target_size: Target size (width, height)
        keep_aspect_ratio: Whether to keep aspect ratio
        interpolation: Interpolation method
        
    Returns:
        Resized image
    """
    if not keep_aspect_ratio:
        return cv2.resize(image, target_size, interpolation=interpolation)
    
    h, w = image.shape[:2]
    target_w, target_h = target_size
    
    # Calculate scaling factor
    scale = min(target_w / w, target_h / h)
    new_w = int(w * scale)
    new_h = int(h * scale)
    
    # Resize image
    resized = cv2.resize(image, (new_w, new_h), interpolation=interpolation)
    
    # Create canvas and center the image
    canvas = np.zeros((target_h, target_w, image.shape[2]), dtype=image.dtype)
    y_offset = (target_h - new_h) // 2
    x_offset = (target_w - new_w) // 2
    canvas[y_offset:y_offset + new_h, x_offset:x_offset + new_w] = resized
    
    return canvas


def convert_image_format(image: np.ndarray, target_format: str) -> np.ndarray:
    """
    Convert image color format.
    
    Args:
        image: Input image
        target_format: Target format (RGB, BGR, GRAY, HSV, etc.)
        
    Returns:
        Converted image
    """
    if len(image.shape) == 2:
        # Grayscale image
        if target_format.upper() == 'RGB':
            return cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        elif target_format.upper() == 'BGR':
            return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
        else:
            return image
    
    elif len(image.shape) == 3:
        # Color image
        if target_format.upper() == 'RGB':
            return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        elif target_format.upper() == 'BGR':
            return cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        elif target_format.upper() == 'GRAY':
            return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        elif target_format.upper() == 'HSV':
            return cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        else:
            return image
    
    return image


class FPSCounter:
    """FPS counter utility class."""
    
    def __init__(self, window_size: int = 30):
        """
        Initialize FPS counter.
        
        Args:
            window_size: Number of frames to average over
        """
        self.window_size = window_size
        self.frame_times = []
        self.last_time = time.time()
    
    def update(self) -> float:
        """
        Update FPS counter and return current FPS.
        
        Returns:
            Current FPS
        """
        current_time = time.time()
        frame_time = current_time - self.last_time
        self.last_time = current_time
        
        self.frame_times.append(frame_time)
        if len(self.frame_times) > self.window_size:
            self.frame_times.pop(0)
        
        if len(self.frame_times) > 0:
            avg_frame_time = sum(self.frame_times) / len(self.frame_times)
            return 1.0 / avg_frame_time if avg_frame_time > 0 else 0.0
        
        return 0.0
    
    def reset(self) -> None:
        """Reset FPS counter."""
        self.frame_times.clear()
        self.last_time = time.time()


class ThreadSafeCounter:
    """Thread-safe counter utility class."""
    
    def __init__(self, initial_value: int = 0):
        """
        Initialize counter.
        
        Args:
            initial_value: Initial counter value
        """
        self._value = initial_value
        self._lock = threading.Lock()
    
    def increment(self, amount: int = 1) -> int:
        """
        Increment counter.
        
        Args:
            amount: Amount to increment
            
        Returns:
            New counter value
        """
        with self._lock:
            self._value += amount
            return self._value
    
    def decrement(self, amount: int = 1) -> int:
        """
        Decrement counter.
        
        Args:
            amount: Amount to decrement
            
        Returns:
            New counter value
        """
        with self._lock:
            self._value -= amount
            return self._value
    
    def get(self) -> int:
        """Get current counter value."""
        with self._lock:
            return self._value
    
    def set(self, value: int) -> None:
        """Set counter value."""
        with self._lock:
            self._value = value
    
    def reset(self) -> None:
        """Reset counter to zero."""
        with self._lock:
            self._value = 0


class CircularBuffer:
    """Circular buffer implementation."""
    
    def __init__(self, size: int):
        """
        Initialize circular buffer.
        
        Args:
            size: Buffer size
        """
        self.size = size
        self.buffer = [None] * size
        self.head = 0
        self.count = 0
        self._lock = threading.Lock()
    
    def put(self, item: Any) -> None:
        """
        Add item to buffer.
        
        Args:
            item: Item to add
        """
        with self._lock:
            self.buffer[self.head] = item
            self.head = (self.head + 1) % self.size
            if self.count < self.size:
                self.count += 1
    
    def get_all(self) -> List[Any]:
        """Get all items in buffer."""
        with self._lock:
            if self.count == 0:
                return []
            
            if self.count < self.size:
                return [item for item in self.buffer[:self.count] if item is not None]
            else:
                # Buffer is full, return in correct order
                return (self.buffer[self.head:] + self.buffer[:self.head])
    
    def get_latest(self, n: int = 1) -> List[Any]:
        """
        Get latest n items.
        
        Args:
            n: Number of items to get
            
        Returns:
            Latest n items
        """
        with self._lock:
            if self.count == 0:
                return []
            
            items = []
            for i in range(min(n, self.count)):
                idx = (self.head - 1 - i) % self.size
                items.append(self.buffer[idx])
            
            return items[::-1]  # Reverse to get chronological order
    
    def clear(self) -> None:
        """Clear buffer."""
        with self._lock:
            self.buffer = [None] * self.size
            self.head = 0
            self.count = 0
    
    def is_empty(self) -> bool:
        """Check if buffer is empty."""
        with self._lock:
            return self.count == 0
    
    def is_full(self) -> bool:
        """Check if buffer is full."""
        with self._lock:
            return self.count == self.size

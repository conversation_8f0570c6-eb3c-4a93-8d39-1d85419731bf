# YOLO Camera System Requirements

# GUI Framework
# PyQt6>=6.5.0
# PyQt6-tools>=6.5.0

# Computer Vision and Image Processing
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0

# Machine Learning - YOLO
ultralytics>=8.0.0
torch>=2.0.0
torchvision>=0.15.0

# Data Processing
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Configuration and Utilities
PyYAML>=6.0
configparser>=5.3.0
pathlib>=1.0.1

# Logging and Monitoring
loguru>=0.7.0

# Network and Communication
requests>=2.31.0

# Development and Testing
pytest>=7.4.0
pytest-qt>=4.2.0
black>=23.0.0
flake8>=6.0.0

# Optional: GPU acceleration (uncomment if using CUDA)
# torch-audio>=2.0.0+cu118
# torchaudio>=2.0.0+cu118

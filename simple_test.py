#!/usr/bin/env python3
"""
Simple test to verify basic functionality
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_basic_imports():
    """Test basic module imports."""
    print("Testing basic imports...")
    
    try:
        # Test config
        from src.config.config_manager import ConfigManager
        config = ConfigManager()
        print("✓ Config manager working")
        
        # Test logger
        from src.logger.logger_setup import setup_logger
        setup_logger(console_output=True, log_level="INFO")
        print("✓ Logger working")
        
        # Test utils
        from src.utils.common import FPSCounter
        fps = FPSCounter()
        print("✓ Utils working")
        
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False

def test_config():
    """Test configuration."""
    print("\nTesting configuration...")
    
    try:
        from src.config.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # Test basic operations
        config.set('test.value', 42)
        value = config.get('test.value')
        assert value == 42
        
        print("✓ Configuration working")
        return True
        
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

def main():
    """Run simple tests."""
    print("YOLO Camera System - Simple Test")
    print("=" * 35)
    
    tests = [
        test_basic_imports,
        test_config
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\nResults: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("✓ Basic functionality working!")
    else:
        print("✗ Some tests failed")
    
    return 0 if passed == len(tests) else 1

if __name__ == "__main__":
    sys.exit(main())

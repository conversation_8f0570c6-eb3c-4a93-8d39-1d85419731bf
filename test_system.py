#!/usr/bin/env python3
"""
YOLO Camera System - Test Script

Simple test script to verify system components and functionality.
"""

import sys
import os
from pathlib import Path
import numpy as np
import cv2

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """Test if all modules can be imported."""
    print("Testing imports...")
    
    try:
        from src.config import ConfigManager
        print("✓ Config module imported successfully")
    except ImportError as e:
        print(f"✗ Config module import failed: {e}")
        return False
    
    try:
        from src.logger import setup_logger
        print("✓ Logger module imported successfully")
    except ImportError as e:
        print(f"✗ Logger module import failed: {e}")
        return False
    
    try:
        from src.utils import FPSCounter, ensure_dir
        print("✓ Utils module imported successfully")
    except ImportError as e:
        print(f"✗ Utils module import failed: {e}")
        return False
    
    try:
        from src.camera import CameraController
        print("✓ Camera module imported successfully")
    except ImportError as e:
        print(f"✗ Camera module import failed: {e}")
        return False
    
    try:
        from src.yolo import YOLODetector
        print("✓ YOLO module imported successfully")
    except ImportError as e:
        print(f"✗ YOLO module import failed: {e}")
        return False
    
    try:
        from src.annotation import AnnotationTool
        print("✓ Annotation module imported successfully")
    except ImportError as e:
        print(f"✗ Annotation module import failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration management."""
    print("\nTesting configuration...")
    
    try:
        from src.config import ConfigManager
        
        # Test config loading
        config = ConfigManager()
        
        # Test getting values
        app_name = config.get('app.name', 'Unknown')
        print(f"✓ App name: {app_name}")
        
        # Test setting values
        config.set('test.value', 123)
        test_value = config.get('test.value')
        assert test_value == 123
        print("✓ Config set/get working")
        
        # Test validation
        is_valid = config.validate_config()
        print(f"✓ Config validation: {'passed' if is_valid else 'failed'}")
        
        return True
        
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

def test_logger():
    """Test logging functionality."""
    print("\nTesting logger...")
    
    try:
        from src.logger import setup_logger, get_logger
        
        # Setup logger
        setup_logger(log_level="INFO", console_output=True)
        
        # Get logger and test
        logger = get_logger("test")
        logger.info("Test log message")
        print("✓ Logger working")
        
        return True
        
    except Exception as e:
        print(f"✗ Logger test failed: {e}")
        return False

def test_utils():
    """Test utility functions."""
    print("\nTesting utilities...")
    
    try:
        from src.utils import FPSCounter, ensure_dir, get_timestamp
        
        # Test FPS counter
        fps_counter = FPSCounter()
        fps = fps_counter.update()
        print(f"✓ FPS counter: {fps:.2f}")
        
        # Test directory creation
        test_dir = Path("test_output")
        ensure_dir(test_dir)
        assert test_dir.exists()
        print("✓ Directory creation working")
        
        # Test timestamp
        timestamp = get_timestamp()
        print(f"✓ Timestamp: {timestamp}")
        
        # Cleanup
        test_dir.rmdir()
        
        return True
        
    except Exception as e:
        print(f"✗ Utils test failed: {e}")
        return False

def test_camera():
    """Test camera controller (without actual camera)."""
    print("\nTesting camera controller...")
    
    try:
        from src.camera import CameraController
        
        # Test initialization
        config = {
            'trigger_mode': 'continuous',
            'frame_rate': 30,
            'buffer_size': 10
        }
        camera = CameraController(config)
        print("✓ Camera controller initialized")
        
        # Test device enumeration (may fail without cameras)
        try:
            devices = camera.enumerate_devices()
            print(f"✓ Found {len(devices)} camera devices")
        except Exception as e:
            print(f"⚠ Camera enumeration failed (no cameras?): {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Camera test failed: {e}")
        return False

def test_yolo():
    """Test YOLO detector."""
    print("\nTesting YOLO detector...")
    
    try:
        from src.yolo import YOLODetector
        
        # Test initialization
        config = {
            'model_path': 'models/yolov11n.pt',
            'confidence_threshold': 0.5,
            'device': 'cpu'
        }
        detector = YOLODetector(config)
        print("✓ YOLO detector initialized")
        
        # Test with dummy image
        dummy_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Note: This will fail without a model, but tests the interface
        try:
            detections = detector.detect(dummy_image)
            print(f"✓ Detection completed: {len(detections)} objects")
        except Exception as e:
            print(f"⚠ Detection failed (no model?): {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ YOLO test failed: {e}")
        return False

def test_annotation():
    """Test annotation tool."""
    print("\nTesting annotation tool...")
    
    try:
        from src.annotation import AnnotationTool, BoundingBox
        
        # Test initialization
        config = {
            'format': 'yolo',
            'auto_save': False
        }
        annotator = AnnotationTool(config)
        print("✓ Annotation tool initialized")
        
        # Test bounding box
        bbox = BoundingBox(100, 100, 50, 30, class_name="test")
        corners = bbox.get_corners()
        print(f"✓ BoundingBox created with {len(corners)} corners")
        
        # Test with dummy image
        dummy_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        annotator.load_image(dummy_image)
        
        # Test adding annotation
        annotator.class_names = ["test_class"]
        success = annotator.add_annotation(320, 240, 100, 80, "test_class")
        print(f"✓ Annotation added: {success}")
        
        return True
        
    except Exception as e:
        print(f"✗ Annotation test failed: {e}")
        return False

def test_gui_imports():
    """Test GUI module imports."""
    print("\nTesting GUI imports...")
    
    try:
        # Test PyQt6 availability
        from PyQt6.QtWidgets import QApplication
        print("✓ PyQt6 available")
        
        # Test GUI module
        from src.gui import MainWindow
        print("✓ GUI module imported")
        
        return True
        
    except ImportError as e:
        print(f"✗ GUI import failed: {e}")
        print("Note: GUI requires PyQt6 to be installed")
        return False

def main():
    """Run all tests."""
    print("YOLO Camera System - Component Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_config,
        test_logger,
        test_utils,
        test_camera,
        test_yolo,
        test_annotation,
        test_gui_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready.")
        return 0
    else:
        print("⚠ Some tests failed. Check dependencies and configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())

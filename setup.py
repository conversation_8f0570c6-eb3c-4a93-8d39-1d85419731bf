#!/usr/bin/env python3
"""
Setup script for YOLO Camera System
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✓ Python version: {sys.version}")
    return True

def create_directories():
    """Create necessary directories."""
    directories = [
        "logs",
        "models", 
        "data/images",
        "data/annotations",
        "data/training",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def install_dependencies():
    """Install Python dependencies."""
    print("Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install dependencies: {e}")
        return False

def test_imports():
    """Test if critical modules can be imported."""
    print("Testing imports...")
    
    critical_modules = [
        "PyQt6",
        "ultralytics", 
        "cv2",
        "numpy",
        "yaml"
    ]
    
    for module in critical_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module}: {e}")
            return False
    
    return True

def run_system_test():
    """Run the system test script."""
    print("Running system tests...")
    
    try:
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ System tests passed")
            return True
        else:
            print("✗ System tests failed")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Failed to run system tests: {e}")
        return False

def main():
    """Main setup function."""
    print("YOLO Camera System Setup")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("\nSetup failed at dependency installation")
        return 1
    
    # Test imports
    if not test_imports():
        print("\nSetup failed at import testing")
        return 1
    
    # Run system tests
    if not run_system_test():
        print("\nWarning: System tests failed, but setup completed")
        print("You may still be able to run the application")
    
    print("\n" + "=" * 30)
    print("Setup completed successfully!")
    print("\nTo run the application:")
    print("  python main.py")
    print("  or")
    print("  run_yolo_camera.bat (Windows)")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

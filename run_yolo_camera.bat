@echo off
echo YOLO Camera System Launcher
echo ===========================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo No virtual environment found. Using system Python.
)

REM Check if requirements are installed
echo Checking dependencies...
python -c "import PyQt6, ultralytics, opencv-python" >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Create necessary directories
if not exist "logs" mkdir logs
if not exist "models" mkdir models
if not exist "data\images" mkdir data\images
if not exist "data\annotations" mkdir data\annotations

REM Run the application
echo Starting YOLO Camera System...
python main.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo Application exited with error
    pause
)

"""
YOLO Detector for YOLO Camera System
Handles YOLO model loading, inference, and training operations.
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
import cv2
import torch
from ultralytics import YOLO
from ultralytics.utils import ops
from loguru import logger

from ..utils import ensure_dir, get_timestamp, save_json, load_json
from ..logger import LoggerMixin, log_performance


class YOLODetector(LoggerMixin):
    """YOLO detector for object detection and training."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize YOLO detector.
        
        Args:
            config: YOLO configuration dictionary
        """
        self.config = config
        self.model = None
        self.device = config.get('device', 'cpu')
        self.confidence_threshold = config.get('confidence_threshold', 0.5)
        self.iou_threshold = config.get('iou_threshold', 0.45)
        self.max_detections = config.get('max_detections', 100)
        self.input_size = config.get('input_size', 640)
        self.classes = config.get('classes', [])
        
        # Model management
        self.model_path = config.get('model_path', 'models/yolov11n.pt')
        self.models_dir = Path('models')
        self.training_dir = Path('data/training')
        
        # Training history
        self.training_history = []
        self.current_version = 0
        
        # Performance tracking
        self.inference_times = []
        self.detection_counts = []
        
        self.logger.info("YOLO detector initialized")
    
    def load_model(self, model_path: Optional[str] = None) -> bool:
        """
        Load YOLO model.
        
        Args:
            model_path: Path to model file. If None, uses config path.
            
        Returns:
            True if model loaded successfully, False otherwise
        """
        try:
            if model_path is None:
                model_path = self.model_path
            
            model_path = Path(model_path)
            
            # Check if model file exists
            if not model_path.exists():
                self.logger.warning(f"Model file not found: {model_path}")
                # Try to download default model
                if not self._download_default_model():
                    return False
            
            # Load model
            self.model = YOLO(str(model_path))
            
            # Set device
            if torch.cuda.is_available() and 'cuda' in self.device:
                self.model.to('cuda')
                self.logger.info(f"Model loaded on GPU: {torch.cuda.get_device_name()}")
            else:
                self.model.to('cpu')
                self.logger.info("Model loaded on CPU")
            
            self.logger.info(f"YOLO model loaded from: {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading YOLO model: {e}")
            return False
    
    def _download_default_model(self) -> bool:
        """
        Download default YOLO model.
        
        Returns:
            True if download successful, False otherwise
        """
        try:
            ensure_dir(self.models_dir)
            
            # Download YOLOv11n model (smallest)
            self.logger.info("Downloading default YOLOv11n model...")
            model = YOLO('yolov11n.pt')
            
            # Save to models directory
            default_path = self.models_dir / 'yolov11n.pt'
            model.save(str(default_path))
            
            self.model_path = str(default_path)
            self.logger.info(f"Default model downloaded to: {default_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error downloading default model: {e}")
            return False
    
    @log_performance
    def detect(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        Perform object detection on image.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of detection results
        """
        if self.model is None:
            self.logger.error("Model not loaded")
            return []
        
        try:
            start_time = time.time()
            
            # Run inference
            results = self.model(
                image,
                conf=self.confidence_threshold,
                iou=self.iou_threshold,
                max_det=self.max_detections,
                classes=self.classes if self.classes else None,
                verbose=False
            )
            
            # Process results
            detections = []
            if results and len(results) > 0:
                result = results[0]
                
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()  # x1, y1, x2, y2
                    confidences = result.boxes.conf.cpu().numpy()
                    class_ids = result.boxes.cls.cpu().numpy().astype(int)
                    
                    for i in range(len(boxes)):
                        x1, y1, x2, y2 = boxes[i]
                        confidence = float(confidences[i])
                        class_id = int(class_ids[i])
                        class_name = self.model.names[class_id]
                        
                        detection = {
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],
                            'confidence': confidence,
                            'class_id': class_id,
                            'class_name': class_name,
                            'center': [float((x1 + x2) / 2), float((y1 + y2) / 2)],
                            'width': float(x2 - x1),
                            'height': float(y2 - y1)
                        }
                        detections.append(detection)
            
            # Update performance tracking
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            self.detection_counts.append(len(detections))
            
            # Keep only recent performance data
            if len(self.inference_times) > 100:
                self.inference_times = self.inference_times[-100:]
                self.detection_counts = self.detection_counts[-100:]
            
            return detections
            
        except Exception as e:
            self.logger.error(f"Error during detection: {e}")
            return []
    
    def draw_detections(
        self,
        image: np.ndarray,
        detections: List[Dict[str, Any]],
        colors: Optional[Dict[str, Tuple[int, int, int]]] = None,
        line_thickness: int = 2,
        font_scale: float = 0.5
    ) -> np.ndarray:
        """
        Draw detection results on image.
        
        Args:
            image: Input image
            detections: Detection results
            colors: Color mapping for classes
            line_thickness: Line thickness for bounding boxes
            font_scale: Font scale for text
            
        Returns:
            Image with drawn detections
        """
        if not detections:
            return image.copy()
        
        result_image = image.copy()
        
        # Default colors
        if colors is None:
            colors = {}
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            confidence = detection['confidence']
            class_name = detection['class_name']
            
            # Get color for class
            if class_name in colors:
                color = colors[class_name]
            else:
                # Generate color based on class name hash
                hash_val = hash(class_name) % (256 * 256 * 256)
                color = (
                    (hash_val >> 16) & 255,
                    (hash_val >> 8) & 255,
                    hash_val & 255
                )
            
            # Draw bounding box
            cv2.rectangle(
                result_image,
                (int(x1), int(y1)),
                (int(x2), int(y2)),
                color,
                line_thickness
            )
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
            
            # Draw label background
            cv2.rectangle(
                result_image,
                (int(x1), int(y1) - label_size[1] - 10),
                (int(x1) + label_size[0], int(y1)),
                color,
                -1
            )
            
            # Draw label text
            cv2.putText(
                result_image,
                label,
                (int(x1), int(y1) - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                font_scale,
                (255, 255, 255),
                1
            )
        
        return result_image
    
    def train_incremental(
        self,
        dataset_path: str,
        epochs: int = 10,
        batch_size: int = 16,
        learning_rate: float = 0.001,
        save_best_only: bool = True
    ) -> bool:
        """
        Perform incremental training on new data.
        
        Args:
            dataset_path: Path to training dataset
            epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate
            save_best_only: Whether to save only the best model
            
        Returns:
            True if training successful, False otherwise
        """
        try:
            if self.model is None:
                self.logger.error("Model not loaded")
                return False
            
            dataset_path = Path(dataset_path)
            if not dataset_path.exists():
                self.logger.error(f"Dataset path not found: {dataset_path}")
                return False
            
            # Create training output directory
            timestamp = get_timestamp()
            training_output_dir = self.models_dir / f"training_{timestamp}"
            ensure_dir(training_output_dir)
            
            self.logger.info(f"Starting incremental training with {epochs} epochs")
            
            # Train model
            results = self.model.train(
                data=str(dataset_path / 'data.yaml'),
                epochs=epochs,
                batch=batch_size,
                lr0=learning_rate,
                project=str(training_output_dir),
                name='incremental',
                exist_ok=True,
                verbose=True
            )
            
            # Save training results
            training_info = {
                'timestamp': timestamp,
                'version': self.current_version + 1,
                'dataset_path': str(dataset_path),
                'epochs': epochs,
                'batch_size': batch_size,
                'learning_rate': learning_rate,
                'results': results.results_dict if hasattr(results, 'results_dict') else {},
                'model_path': str(training_output_dir / 'incremental' / 'weights' / 'best.pt')
            }
            
            # Update training history
            self.training_history.append(training_info)
            self.current_version += 1
            
            # Save training history
            history_file = self.models_dir / 'training_history.json'
            save_json(self.training_history, history_file)
            
            # Load the new trained model if requested
            if save_best_only:
                best_model_path = training_output_dir / 'incremental' / 'weights' / 'best.pt'
                if best_model_path.exists():
                    self.load_model(str(best_model_path))
                    self.model_path = str(best_model_path)
            
            self.logger.info(f"Incremental training completed. Version: {self.current_version}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during incremental training: {e}")
            return False
    
    def load_training_history(self) -> bool:
        """
        Load training history from file.
        
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            history_file = self.models_dir / 'training_history.json'
            if history_file.exists():
                self.training_history = load_json(history_file)
                if self.training_history:
                    self.current_version = max(item['version'] for item in self.training_history)
                self.logger.info(f"Loaded training history with {len(self.training_history)} entries")
                return True
            else:
                self.logger.info("No training history file found")
                return False
                
        except Exception as e:
            self.logger.error(f"Error loading training history: {e}")
            return False
    
    def rollback_to_version(self, version: int) -> bool:
        """
        Rollback to a specific model version.
        
        Args:
            version: Version number to rollback to
            
        Returns:
            True if rollback successful, False otherwise
        """
        try:
            # Find the version in history
            target_training = None
            for training in self.training_history:
                if training['version'] == version:
                    target_training = training
                    break
            
            if target_training is None:
                self.logger.error(f"Version {version} not found in training history")
                return False
            
            model_path = target_training['model_path']
            if not Path(model_path).exists():
                self.logger.error(f"Model file not found: {model_path}")
                return False
            
            # Load the model
            if self.load_model(model_path):
                self.model_path = model_path
                self.logger.info(f"Rolled back to version {version}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"Error rolling back to version {version}: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics.
        
        Returns:
            Performance statistics dictionary
        """
        if not self.inference_times:
            return {
                'avg_inference_time': 0.0,
                'min_inference_time': 0.0,
                'max_inference_time': 0.0,
                'avg_detections': 0.0,
                'total_inferences': 0
            }
        
        return {
            'avg_inference_time': np.mean(self.inference_times),
            'min_inference_time': np.min(self.inference_times),
            'max_inference_time': np.max(self.inference_times),
            'avg_detections': np.mean(self.detection_counts),
            'total_inferences': len(self.inference_times)
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get model information.
        
        Returns:
            Model information dictionary
        """
        if self.model is None:
            return {'loaded': False}
        
        return {
            'loaded': True,
            'model_path': self.model_path,
            'device': str(self.model.device),
            'model_type': type(self.model).__name__,
            'class_names': list(self.model.names.values()) if hasattr(self.model, 'names') else [],
            'num_classes': len(self.model.names) if hasattr(self.model, 'names') else 0,
            'current_version': self.current_version,
            'training_history_count': len(self.training_history)
        }

#!/usr/bin/env python3
"""
YOLO Camera System - Main Entry Point

A real-time camera capture and object detection system using YOLO models
with annotation capabilities and incremental training support.

Features:
- Real-time camera capture with MvImport SDK
- YOLO object detection and inference
- Interactive image annotation tools
- Incremental model training
- Configuration management
- PyQt6-based GUI interface

Author: YOLO Camera System Team
Version: 1.0.0
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import and run the main application
if __name__ == "__main__":
    try:
        from src.gui import main
        main()
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Application error: {e}")
        sys.exit(1)

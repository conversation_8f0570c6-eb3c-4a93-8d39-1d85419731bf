"""
Annotation Tool for YOLO Camera System
Handles image annotation including bounding boxes, rotation, and labeling.
"""

import json
import math
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
import cv2
from loguru import logger

from ..utils import ensure_dir, get_timestamp, save_json, load_json
from ..logger import LoggerMixin


class BoundingBox:
    """Bounding box class with rotation support."""
    
    def __init__(
        self,
        x: float,
        y: float,
        width: float,
        height: float,
        angle: float = 0.0,
        class_id: int = 0,
        class_name: str = "",
        confidence: float = 1.0
    ):
        """
        Initialize bounding box.
        
        Args:
            x: Center x coordinate
            y: Center y coordinate
            width: Box width
            height: Box height
            angle: Rotation angle in degrees
            class_id: Class ID
            class_name: Class name
            confidence: Confidence score
        """
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.angle = angle
        self.class_id = class_id
        self.class_name = class_name
        self.confidence = confidence
    
    def get_corners(self) -> List[Tuple[float, float]]:
        """
        Get the four corners of the rotated bounding box.
        
        Returns:
            List of (x, y) corner coordinates
        """
        # Half dimensions
        hw = self.width / 2
        hh = self.height / 2
        
        # Rotation angle in radians
        angle_rad = math.radians(self.angle)
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        
        # Corner offsets from center
        corners = [
            (-hw, -hh),  # Top-left
            (hw, -hh),   # Top-right
            (hw, hh),    # Bottom-right
            (-hw, hh)    # Bottom-left
        ]
        
        # Rotate and translate corners
        rotated_corners = []
        for dx, dy in corners:
            rx = dx * cos_a - dy * sin_a + self.x
            ry = dx * sin_a + dy * cos_a + self.y
            rotated_corners.append((rx, ry))
        
        return rotated_corners
    
    def get_axis_aligned_bbox(self) -> Tuple[float, float, float, float]:
        """
        Get axis-aligned bounding box that contains the rotated box.
        
        Returns:
            (x_min, y_min, x_max, y_max)
        """
        corners = self.get_corners()
        x_coords = [corner[0] for corner in corners]
        y_coords = [corner[1] for corner in corners]
        
        return (
            min(x_coords),
            min(y_coords),
            max(x_coords),
            max(y_coords)
        )
    
    def contains_point(self, px: float, py: float) -> bool:
        """
        Check if a point is inside the bounding box.
        
        Args:
            px: Point x coordinate
            py: Point y coordinate
            
        Returns:
            True if point is inside, False otherwise
        """
        # Transform point to box coordinate system
        angle_rad = math.radians(-self.angle)  # Negative for inverse rotation
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        
        # Translate to box center
        dx = px - self.x
        dy = py - self.y
        
        # Rotate
        rx = dx * cos_a - dy * sin_a
        ry = dx * sin_a + dy * cos_a
        
        # Check if inside axis-aligned box
        return (abs(rx) <= self.width / 2 and abs(ry) <= self.height / 2)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height,
            'angle': self.angle,
            'class_id': self.class_id,
            'class_name': self.class_name,
            'confidence': self.confidence
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BoundingBox':
        """Create from dictionary."""
        return cls(
            x=data['x'],
            y=data['y'],
            width=data['width'],
            height=data['height'],
            angle=data.get('angle', 0.0),
            class_id=data.get('class_id', 0),
            class_name=data.get('class_name', ''),
            confidence=data.get('confidence', 1.0)
        )


class AnnotationTool(LoggerMixin):
    """Annotation tool for image labeling."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize annotation tool.
        
        Args:
            config: Annotation configuration dictionary
        """
        self.config = config
        self.annotations_dir = Path(config.get('base_dir', 'data/annotations'))
        self.format = config.get('format', 'yolo')
        self.auto_save = config.get('auto_save', True)
        
        # Colors and styling
        self.colors = config.get('colors', {
            'bbox': [0, 255, 0],
            'text': [255, 255, 255],
            'background': [0, 0, 0]
        })
        self.line_thickness = config.get('line_thickness', 2)
        self.font_scale = config.get('font_scale', 0.5)
        
        # Class management
        self.class_names = []
        self.class_colors = {}
        
        # Current annotation state
        self.current_image = None
        self.current_annotations = []
        self.current_image_path = None
        
        ensure_dir(self.annotations_dir)
        self.logger.info("Annotation tool initialized")
    
    def load_classes(self, classes_file: Union[str, Path, List[str]]) -> bool:
        """
        Load class names.
        
        Args:
            classes_file: Path to classes file or list of class names
            
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            if isinstance(classes_file, list):
                self.class_names = classes_file
            else:
                classes_file = Path(classes_file)
                if classes_file.exists():
                    with open(classes_file, 'r', encoding='utf-8') as f:
                        self.class_names = [line.strip() for line in f if line.strip()]
                else:
                    self.logger.warning(f"Classes file not found: {classes_file}")
                    return False
            
            # Generate colors for classes
            self._generate_class_colors()
            
            self.logger.info(f"Loaded {len(self.class_names)} classes")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading classes: {e}")
            return False
    
    def _generate_class_colors(self) -> None:
        """Generate colors for each class."""
        self.class_colors = {}
        for i, class_name in enumerate(self.class_names):
            # Generate color based on class index
            hue = (i * 137.5) % 360  # Golden angle for good color distribution
            color = self._hsv_to_bgr(hue, 0.8, 0.9)
            self.class_colors[class_name] = color
    
    def _hsv_to_bgr(self, h: float, s: float, v: float) -> Tuple[int, int, int]:
        """Convert HSV to BGR color."""
        import colorsys
        r, g, b = colorsys.hsv_to_rgb(h / 360, s, v)
        return (int(b * 255), int(g * 255), int(r * 255))
    
    def load_image(self, image_path: Union[str, Path, np.ndarray]) -> bool:
        """
        Load image for annotation.
        
        Args:
            image_path: Path to image file or numpy array
            
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            if isinstance(image_path, np.ndarray):
                self.current_image = image_path.copy()
                self.current_image_path = None
            else:
                image_path = Path(image_path)
                if not image_path.exists():
                    self.logger.error(f"Image file not found: {image_path}")
                    return False
                
                self.current_image = cv2.imread(str(image_path))
                if self.current_image is None:
                    self.logger.error(f"Failed to load image: {image_path}")
                    return False
                
                self.current_image_path = image_path
            
            # Load existing annotations if available
            self.current_annotations = []
            if self.current_image_path:
                self._load_annotations_for_image(self.current_image_path)
            
            self.logger.info(f"Image loaded with {len(self.current_annotations)} existing annotations")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading image: {e}")
            return False
    
    def add_annotation(
        self,
        x: float,
        y: float,
        width: float,
        height: float,
        class_name: str,
        angle: float = 0.0,
        confidence: float = 1.0
    ) -> bool:
        """
        Add annotation to current image.
        
        Args:
            x: Center x coordinate
            y: Center y coordinate
            width: Box width
            height: Box height
            class_name: Class name
            angle: Rotation angle in degrees
            confidence: Confidence score
            
        Returns:
            True if added successfully, False otherwise
        """
        try:
            if self.current_image is None:
                self.logger.error("No image loaded")
                return False
            
            if class_name not in self.class_names:
                self.logger.warning(f"Unknown class name: {class_name}")
                # Add to class names if not exists
                self.class_names.append(class_name)
                self._generate_class_colors()
            
            class_id = self.class_names.index(class_name)
            
            bbox = BoundingBox(
                x=x, y=y, width=width, height=height,
                angle=angle, class_id=class_id, class_name=class_name,
                confidence=confidence
            )
            
            self.current_annotations.append(bbox)
            
            if self.auto_save and self.current_image_path:
                self.save_annotations()
            
            self.logger.debug(f"Added annotation: {class_name} at ({x}, {y})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding annotation: {e}")
            return False
    
    def remove_annotation(self, index: int) -> bool:
        """
        Remove annotation by index.
        
        Args:
            index: Annotation index
            
        Returns:
            True if removed successfully, False otherwise
        """
        try:
            if 0 <= index < len(self.current_annotations):
                removed = self.current_annotations.pop(index)
                
                if self.auto_save and self.current_image_path:
                    self.save_annotations()
                
                self.logger.debug(f"Removed annotation: {removed.class_name}")
                return True
            else:
                self.logger.warning(f"Invalid annotation index: {index}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error removing annotation: {e}")
            return False
    
    def find_annotation_at_point(self, x: float, y: float) -> Optional[int]:
        """
        Find annotation at given point.
        
        Args:
            x: Point x coordinate
            y: Point y coordinate
            
        Returns:
            Annotation index or None if not found
        """
        for i, bbox in enumerate(self.current_annotations):
            if bbox.contains_point(x, y):
                return i
        return None
    
    def draw_annotations(
        self,
        image: Optional[np.ndarray] = None,
        show_labels: bool = True,
        show_confidence: bool = True
    ) -> np.ndarray:
        """
        Draw annotations on image.
        
        Args:
            image: Image to draw on. If None, uses current image.
            show_labels: Whether to show class labels
            show_confidence: Whether to show confidence scores
            
        Returns:
            Image with drawn annotations
        """
        if image is None:
            if self.current_image is None:
                raise ValueError("No image available")
            image = self.current_image.copy()
        else:
            image = image.copy()
        
        for bbox in self.current_annotations:
            # Get color for class
            color = self.class_colors.get(bbox.class_name, (0, 255, 0))
            
            if bbox.angle == 0:
                # Draw regular rectangle
                x1 = int(bbox.x - bbox.width / 2)
                y1 = int(bbox.y - bbox.height / 2)
                x2 = int(bbox.x + bbox.width / 2)
                y2 = int(bbox.y + bbox.height / 2)
                
                cv2.rectangle(image, (x1, y1), (x2, y2), color, self.line_thickness)
            else:
                # Draw rotated rectangle
                corners = bbox.get_corners()
                points = np.array(corners, dtype=np.int32)
                cv2.polylines(image, [points], True, color, self.line_thickness)
            
            # Draw label
            if show_labels:
                label = bbox.class_name
                if show_confidence and bbox.confidence < 1.0:
                    label += f": {bbox.confidence:.2f}"
                
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, 1)[0]
                
                # Position label
                label_x = int(bbox.x - label_size[0] / 2)
                label_y = int(bbox.y - bbox.height / 2 - 10)
                
                # Draw label background
                cv2.rectangle(
                    image,
                    (label_x - 2, label_y - label_size[1] - 2),
                    (label_x + label_size[0] + 2, label_y + 2),
                    color,
                    -1
                )
                
                # Draw label text
                cv2.putText(
                    image,
                    label,
                    (label_x, label_y),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    self.font_scale,
                    (255, 255, 255),
                    1
                )
        
        return image

    def save_annotations(self, output_path: Optional[Union[str, Path]] = None) -> bool:
        """
        Save annotations to file.

        Args:
            output_path: Output file path. If None, uses default naming.

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            if not self.current_annotations:
                self.logger.debug("No annotations to save")
                return True

            if output_path is None:
                if self.current_image_path is None:
                    self.logger.error("No image path available for saving annotations")
                    return False

                # Generate annotation file path
                image_stem = self.current_image_path.stem
                if self.format == 'yolo':
                    output_path = self.annotations_dir / f"{image_stem}.txt"
                elif self.format == 'coco':
                    output_path = self.annotations_dir / f"{image_stem}.json"
                else:
                    output_path = self.annotations_dir / f"{image_stem}.json"

            output_path = Path(output_path)
            ensure_dir(output_path.parent)

            if self.format == 'yolo':
                self._save_yolo_format(output_path)
            elif self.format == 'coco':
                self._save_coco_format(output_path)
            else:
                self._save_json_format(output_path)

            self.logger.debug(f"Saved {len(self.current_annotations)} annotations to {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error saving annotations: {e}")
            return False

    def _save_yolo_format(self, output_path: Path) -> None:
        """Save annotations in YOLO format."""
        if self.current_image is None:
            raise ValueError("No image loaded")

        img_height, img_width = self.current_image.shape[:2]

        with open(output_path, 'w', encoding='utf-8') as f:
            for bbox in self.current_annotations:
                # Convert to YOLO format (normalized coordinates)
                x_center = bbox.x / img_width
                y_center = bbox.y / img_height
                width = bbox.width / img_width
                height = bbox.height / img_height

                # YOLO format: class_id x_center y_center width height
                f.write(f"{bbox.class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")

    def _save_coco_format(self, output_path: Path) -> None:
        """Save annotations in COCO format."""
        if self.current_image is None:
            raise ValueError("No image loaded")

        img_height, img_width = self.current_image.shape[:2]

        # COCO format structure
        coco_data = {
            "images": [{
                "id": 1,
                "width": img_width,
                "height": img_height,
                "file_name": self.current_image_path.name if self.current_image_path else "image.jpg"
            }],
            "categories": [
                {"id": i, "name": name} for i, name in enumerate(self.class_names)
            ],
            "annotations": []
        }

        for i, bbox in enumerate(self.current_annotations):
            # Convert to COCO bbox format (x, y, width, height)
            x = bbox.x - bbox.width / 2
            y = bbox.y - bbox.height / 2

            annotation = {
                "id": i + 1,
                "image_id": 1,
                "category_id": bbox.class_id,
                "bbox": [x, y, bbox.width, bbox.height],
                "area": bbox.width * bbox.height,
                "iscrowd": 0
            }

            if bbox.angle != 0:
                annotation["angle"] = bbox.angle

            coco_data["annotations"].append(annotation)

        save_json(coco_data, output_path)

    def _save_json_format(self, output_path: Path) -> None:
        """Save annotations in custom JSON format."""
        if self.current_image is None:
            raise ValueError("No image loaded")

        img_height, img_width = self.current_image.shape[:2]

        data = {
            "image": {
                "width": img_width,
                "height": img_height,
                "path": str(self.current_image_path) if self.current_image_path else None
            },
            "annotations": [bbox.to_dict() for bbox in self.current_annotations],
            "classes": self.class_names,
            "timestamp": get_timestamp(),
            "format": "custom_json"
        }

        save_json(data, output_path)

    def load_annotations(self, annotation_path: Union[str, Path]) -> bool:
        """
        Load annotations from file.

        Args:
            annotation_path: Path to annotation file

        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            annotation_path = Path(annotation_path)
            if not annotation_path.exists():
                self.logger.error(f"Annotation file not found: {annotation_path}")
                return False

            self.current_annotations = []

            if annotation_path.suffix == '.txt':
                # Assume YOLO format
                self._load_yolo_format(annotation_path)
            elif annotation_path.suffix == '.json':
                # Try to detect format
                data = load_json(annotation_path)
                if 'annotations' in data and isinstance(data['annotations'], list):
                    if 'format' in data and data['format'] == 'custom_json':
                        self._load_json_format(annotation_path)
                    else:
                        self._load_coco_format(annotation_path)
                else:
                    self.logger.error(f"Unknown JSON annotation format: {annotation_path}")
                    return False
            else:
                self.logger.error(f"Unsupported annotation format: {annotation_path.suffix}")
                return False

            self.logger.info(f"Loaded {len(self.current_annotations)} annotations from {annotation_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error loading annotations: {e}")
            return False

    def _load_yolo_format(self, annotation_path: Path) -> None:
        """Load annotations from YOLO format."""
        if self.current_image is None:
            raise ValueError("No image loaded")

        img_height, img_width = self.current_image.shape[:2]

        with open(annotation_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue

                parts = line.split()
                if len(parts) < 5:
                    continue

                class_id = int(parts[0])
                x_center = float(parts[1]) * img_width
                y_center = float(parts[2]) * img_height
                width = float(parts[3]) * img_width
                height = float(parts[4]) * img_height

                class_name = self.class_names[class_id] if class_id < len(self.class_names) else f"class_{class_id}"

                bbox = BoundingBox(
                    x=x_center, y=y_center, width=width, height=height,
                    class_id=class_id, class_name=class_name
                )
                self.current_annotations.append(bbox)

    def _load_coco_format(self, annotation_path: Path) -> None:
        """Load annotations from COCO format."""
        data = load_json(annotation_path)

        # Build category mapping
        categories = {cat['id']: cat['name'] for cat in data.get('categories', [])}

        for ann in data.get('annotations', []):
            bbox_data = ann['bbox']  # [x, y, width, height]
            x = bbox_data[0] + bbox_data[2] / 2  # Convert to center
            y = bbox_data[1] + bbox_data[3] / 2
            width = bbox_data[2]
            height = bbox_data[3]

            class_id = ann['category_id']
            class_name = categories.get(class_id, f"class_{class_id}")
            angle = ann.get('angle', 0.0)

            bbox = BoundingBox(
                x=x, y=y, width=width, height=height, angle=angle,
                class_id=class_id, class_name=class_name
            )
            self.current_annotations.append(bbox)

    def _load_json_format(self, annotation_path: Path) -> None:
        """Load annotations from custom JSON format."""
        data = load_json(annotation_path)

        # Load classes if available
        if 'classes' in data:
            self.class_names = data['classes']
            self._generate_class_colors()

        for ann_data in data.get('annotations', []):
            bbox = BoundingBox.from_dict(ann_data)
            self.current_annotations.append(bbox)

    def _load_annotations_for_image(self, image_path: Path) -> None:
        """Load annotations for the given image."""
        # Try different annotation file formats
        annotation_files = [
            self.annotations_dir / f"{image_path.stem}.txt",  # YOLO
            self.annotations_dir / f"{image_path.stem}.json",  # JSON
        ]

        for ann_file in annotation_files:
            if ann_file.exists():
                self.load_annotations(ann_file)
                break

    def export_dataset(
        self,
        images_dir: Union[str, Path],
        output_dir: Union[str, Path],
        format: str = 'yolo',
        train_split: float = 0.8
    ) -> bool:
        """
        Export annotated dataset.

        Args:
            images_dir: Directory containing images
            output_dir: Output directory for dataset
            format: Export format ('yolo', 'coco')
            train_split: Training split ratio

        Returns:
            True if exported successfully, False otherwise
        """
        try:
            images_dir = Path(images_dir)
            output_dir = Path(output_dir)

            if not images_dir.exists():
                self.logger.error(f"Images directory not found: {images_dir}")
                return False

            # Create output structure
            if format == 'yolo':
                train_images_dir = output_dir / 'images' / 'train'
                val_images_dir = output_dir / 'images' / 'val'
                train_labels_dir = output_dir / 'labels' / 'train'
                val_labels_dir = output_dir / 'labels' / 'val'

                for dir_path in [train_images_dir, val_images_dir, train_labels_dir, val_labels_dir]:
                    ensure_dir(dir_path)
            else:
                ensure_dir(output_dir)

            # Find all images with annotations
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                image_files.extend(images_dir.glob(f'*{ext}'))
                image_files.extend(images_dir.glob(f'*{ext.upper()}'))

            annotated_images = []
            for img_file in image_files:
                ann_file = self.annotations_dir / f"{img_file.stem}.txt"
                if ann_file.exists():
                    annotated_images.append(img_file)

            if not annotated_images:
                self.logger.error("No annotated images found")
                return False

            # Split dataset
            import random
            random.shuffle(annotated_images)
            split_idx = int(len(annotated_images) * train_split)
            train_images = annotated_images[:split_idx]
            val_images = annotated_images[split_idx:]

            self.logger.info(f"Exporting dataset: {len(train_images)} train, {len(val_images)} val images")

            if format == 'yolo':
                self._export_yolo_dataset(train_images, val_images, output_dir)
            elif format == 'coco':
                self._export_coco_dataset(train_images, val_images, output_dir)

            self.logger.info(f"Dataset exported to: {output_dir}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting dataset: {e}")
            return False

    def _export_yolo_dataset(self, train_images: List[Path], val_images: List[Path], output_dir: Path) -> None:
        """Export dataset in YOLO format."""
        import shutil

        # Copy images and labels
        for split_name, images in [('train', train_images), ('val', val_images)]:
            for img_file in images:
                # Copy image
                dst_img = output_dir / 'images' / split_name / img_file.name
                shutil.copy2(img_file, dst_img)

                # Copy annotation
                ann_file = self.annotations_dir / f"{img_file.stem}.txt"
                if ann_file.exists():
                    dst_ann = output_dir / 'labels' / split_name / f"{img_file.stem}.txt"
                    shutil.copy2(ann_file, dst_ann)

        # Create data.yaml
        data_yaml = {
            'train': str(output_dir / 'images' / 'train'),
            'val': str(output_dir / 'images' / 'val'),
            'nc': len(self.class_names),
            'names': self.class_names
        }

        import yaml
        with open(output_dir / 'data.yaml', 'w', encoding='utf-8') as f:
            yaml.dump(data_yaml, f, default_flow_style=False)

    def _export_coco_dataset(self, train_images: List[Path], val_images: List[Path], output_dir: Path) -> None:
        """Export dataset in COCO format."""
        # Implementation for COCO export would go here
        # This is a simplified version
        self.logger.warning("COCO export not fully implemented")

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get annotation statistics.

        Returns:
            Statistics dictionary
        """
        if not self.current_annotations:
            return {
                'total_annotations': 0,
                'classes': {},
                'avg_bbox_size': 0.0
            }

        class_counts = {}
        bbox_sizes = []

        for bbox in self.current_annotations:
            class_counts[bbox.class_name] = class_counts.get(bbox.class_name, 0) + 1
            bbox_sizes.append(bbox.width * bbox.height)

        return {
            'total_annotations': len(self.current_annotations),
            'classes': class_counts,
            'avg_bbox_size': np.mean(bbox_sizes) if bbox_sizes else 0.0,
            'class_names': self.class_names
        }

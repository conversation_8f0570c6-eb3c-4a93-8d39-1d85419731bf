annotation:
  auto_save: true
  colors:
    background:
    - 0
    - 0
    - 0
    bbox:
    - 0
    - 255
    - 0
    text:
    - 255
    - 255
    - 255
  font_scale: 0.5
  format: yolo
  line_thickness: 2
app:
  debug: false
  log_level: INFO
  name: YOLO Camera System
  version: 1.0.0
camera:
  acquisition_mode: continuous
  buffer_size: 10
  exposure_time: 10000
  frame_rate: 30
  gain: 1.0
  pixel_format: RGB8_Packed
  trigger_mode: continuous
logging:
  console_output: true
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}'
  log_file: logs/yolo_camera.log
  retention: 7 days
  rotation: 10 MB
performance:
  optimize_memory: true
  processing_queue_size: 10
  use_gpu: false
  worker_threads: 4
server:
  enabled: false
  host: localhost
  max_clients: 5
  port: 8888
  send_detections: true
  send_images: false
storage:
  annotated_dir: annotated
  auto_create_dirs: true
  base_dir: data/images
  detected_dir: detected
  jpeg_quality: 95
  raw_dir: raw
  save_format: jpg
training:
  augment: true
  batch_size: 16
  data_dir: data/training
  epochs: 100
  img_size: 640
  learning_rate: 0.001
  save_best_only: true
  val_split: 0.2
ui:
  display_height: 480
  display_width: 640
  show_detection_info: true
  show_fps: true
  update_interval: 33
  window_height: 800
  window_width: 1200
yolo:
  classes: []
  confidence_threshold: 0.5
  device: cpu
  input_size: 640
  iou_threshold: 0.45
  max_detections: 100
  model_path: models/yolov11n.pt

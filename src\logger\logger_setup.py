"""
Logger setup for YOLO Camera System
Provides centralized logging configuration using loguru.
"""

import sys
import os
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    rotation: str = "10 MB",
    retention: str = "7 days",
    console_output: bool = True,
    format_string: Optional[str] = None
) -> None:
    """
    Setup logger configuration for the application.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file. If None, uses default path
        rotation: Log rotation policy
        retention: Log retention policy
        console_output: Whether to output logs to console
        format_string: Custom log format string
    """
    # Remove default logger
    logger.remove()
    
    # Default format
    if format_string is None:
        format_string = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    # Add console handler if requested
    if console_output:
        logger.add(
            sys.stdout,
            level=log_level,
            format=format_string,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    # Add file handler if log file is specified
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_path,
            level=log_level,
            format=format_string,
            rotation=rotation,
            retention=retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
        
        logger.info(f"Logger initialized with file output: {log_path}")
    else:
        logger.info("Logger initialized with console output only")


def get_logger(name: str):
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self):
        """Get logger instance for this class."""
        return logger.bind(name=self.__class__.__name__)


# Exception handler for uncaught exceptions
def exception_handler(exc_type, exc_value, exc_traceback):
    """Handle uncaught exceptions with logger."""
    if issubclass(exc_type, KeyboardInterrupt):
        # Handle Ctrl+C gracefully
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger.critical(
        "Uncaught exception",
        exc_info=(exc_type, exc_value, exc_traceback)
    )


def setup_exception_handling():
    """Setup global exception handling."""
    sys.excepthook = exception_handler


# Context manager for logging function calls
class LoggedFunction:
    """Context manager for logging function entry and exit."""
    
    def __init__(self, func_name: str, log_level: str = "DEBUG"):
        self.func_name = func_name
        self.log_level = log_level
    
    def __enter__(self):
        logger.log(self.log_level, f"Entering function: {self.func_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            logger.log(self.log_level, f"Exiting function: {self.func_name}")
        else:
            logger.error(f"Exception in function {self.func_name}: {exc_val}")


def log_function_call(log_level: str = "DEBUG"):
    """
    Decorator to log function calls.
    
    Args:
        log_level: Log level for the messages
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__qualname__}"
            with LoggedFunction(func_name, log_level):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# Performance logging utilities
import time
from functools import wraps


def log_performance(func):
    """Decorator to log function execution time."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        
        func_name = f"{func.__module__}.{func.__qualname__}"
        logger.debug(f"Function {func_name} executed in {execution_time:.4f} seconds")
        
        return result
    return wrapper


class PerformanceTimer:
    """Context manager for measuring and logging execution time."""
    
    def __init__(self, operation_name: str, log_level: str = "DEBUG"):
        self.operation_name = operation_name
        self.log_level = log_level
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            execution_time = time.time() - self.start_time
            logger.log(
                self.log_level,
                f"Operation '{self.operation_name}' completed in {execution_time:.4f} seconds"
            )


# Memory usage logging (optional, requires psutil)
try:
    import psutil
    
    def log_memory_usage(func):
        """Decorator to log memory usage before and after function execution."""
        @wraps(func)
        def wrapper(*args, **kwargs):
            process = psutil.Process()
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
            
            result = func(*args, **kwargs)
            
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_diff = memory_after - memory_before
            
            func_name = f"{func.__module__}.{func.__qualname__}"
            logger.debug(
                f"Function {func_name} memory usage: "
                f"before={memory_before:.2f}MB, after={memory_after:.2f}MB, "
                f"diff={memory_diff:+.2f}MB"
            )
            
            return result
        return wrapper
    
except ImportError:
    def log_memory_usage(func):
        """Fallback decorator when psutil is not available."""
        return func

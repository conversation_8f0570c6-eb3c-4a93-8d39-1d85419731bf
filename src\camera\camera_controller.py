"""
Camera Controller for YOLO Camera System
Handles camera connection, configuration, and image acquisition using MvImport SDK.
"""

import time
import threading
import queue
from typing import Optional, Callable, Dict, Any, Tuple, List
from ctypes import *
import numpy as np
import cv2
from loguru import logger

# Import MvImport SDK
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from MvImport.MvCameraControl_class import MvCamera
from MvImport.CameraParams_const import *
from MvImport.CameraParams_header import *
from MvImport.MvErrorDefine_const import *
from MvImport.PixelType_header import *

from ..utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, ThreadSafeCounter, CircularBuffer
from ..logger import LoggerMixin


class CameraController(LoggerMixin):
    """Camera controller for handling MvImport camera operations."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize camera controller.
        
        Args:
            config: Camera configuration dictionary
        """
        self.config = config
        self.camera = MvCamera()
        self.device_list = MV_CC_DEVICE_INFO_LIST()
        self.device_info = None
        self.is_connected = False
        self.is_grabbing = False
        
        # Threading and synchronization
        self._grab_thread = None
        self._stop_event = threading.Event()
        self._frame_queue = queue.Queue(maxsize=config.get('buffer_size', 10))
        
        # Statistics
        self.fps_counter = FPSCounter()
        self.frame_counter = ThreadSafeCounter()
        self.error_counter = ThreadSafeCounter()
        
        # Callbacks
        self.frame_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
        
        # Image buffer for latest frames
        self.frame_buffer = CircularBuffer(config.get('buffer_size', 10))
        
        self.logger.info("Camera controller initialized")
    
    def enumerate_devices(self) -> List[Dict[str, Any]]:
        """
        Enumerate available cameras.
        
        Returns:
            List of camera device information
        """
        try:
            # Initialize SDK
            ret = MvCamera.MV_CC_Initialize()
            if ret != MV_OK:
                self.logger.error(f"Failed to initialize SDK: {ret}")
                return []
            
            # Enumerate devices
            ret = MvCamera.MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, self.device_list)
            if ret != MV_OK:
                self.logger.error(f"Failed to enumerate devices: {ret}")
                return []
            
            devices = []
            for i in range(self.device_list.nDeviceNum):
                device_info = cast(self.device_list.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
                
                device_data = {
                    'index': i,
                    'device_type': device_info.nTLayerType,
                    'device_info': device_info
                }
                
                # Extract device-specific information
                if device_info.nTLayerType == MV_GIGE_DEVICE:
                    gige_info = cast(device_info.SpecialInfo.stGigEInfo, MV_GIGE_DEVICE_INFO)
                    device_data.update({
                        'manufacturer': gige_info.chManufacturerName.decode('ascii'),
                        'model': gige_info.chModelName.decode('ascii'),
                        'serial_number': gige_info.chSerialNumber.decode('ascii'),
                        'ip_address': f"{(gige_info.nCurrentIp >> 24) & 0xFF}."
                                    f"{(gige_info.nCurrentIp >> 16) & 0xFF}."
                                    f"{(gige_info.nCurrentIp >> 8) & 0xFF}."
                                    f"{gige_info.nCurrentIp & 0xFF}",
                        'interface_type': 'GigE'
                    })
                elif device_info.nTLayerType == MV_USB_DEVICE:
                    usb_info = cast(device_info.SpecialInfo.stUsbV3Info, MV_USB3_DEVICE_INFO)
                    device_data.update({
                        'manufacturer': usb_info.chManufacturerName.decode('ascii'),
                        'model': usb_info.chModelName.decode('ascii'),
                        'serial_number': usb_info.chSerialNumber.decode('ascii'),
                        'interface_type': 'USB3'
                    })
                
                devices.append(device_data)
                self.logger.debug(f"Found device: {device_data}")
            
            self.logger.info(f"Enumerated {len(devices)} camera devices")
            return devices
            
        except Exception as e:
            self.logger.error(f"Error enumerating devices: {e}")
            return []
    
    def connect(self, device_index: int = 0) -> bool:
        """
        Connect to camera device.
        
        Args:
            device_index: Index of device to connect to
            
        Returns:
            True if connection successful, False otherwise
        """
        try:
            if self.is_connected:
                self.logger.warning("Camera already connected")
                return True
            
            # Enumerate devices first
            devices = self.enumerate_devices()
            if not devices or device_index >= len(devices):
                self.logger.error(f"Invalid device index: {device_index}")
                return False
            
            self.device_info = devices[device_index]['device_info']
            
            # Create handle
            ret = self.camera.MV_CC_CreateHandle(self.device_info)
            if ret != MV_OK:
                self.logger.error(f"Failed to create camera handle: {ret}")
                return False
            
            # Open device
            ret = self.camera.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
            if ret != MV_OK:
                self.logger.error(f"Failed to open camera device: {ret}")
                self.camera.MV_CC_DestroyHandle()
                return False
            
            # Configure camera
            if not self._configure_camera():
                self.disconnect()
                return False
            
            self.is_connected = True
            self.logger.info(f"Successfully connected to camera device {device_index}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error connecting to camera: {e}")
            return False
    
    def disconnect(self) -> bool:
        """
        Disconnect from camera device.
        
        Returns:
            True if disconnection successful, False otherwise
        """
        try:
            if not self.is_connected:
                return True
            
            # Stop grabbing if active
            if self.is_grabbing:
                self.stop_grabbing()
            
            # Close device
            ret = self.camera.MV_CC_CloseDevice()
            if ret != MV_OK:
                self.logger.warning(f"Failed to close camera device: {ret}")
            
            # Destroy handle
            ret = self.camera.MV_CC_DestroyHandle()
            if ret != MV_OK:
                self.logger.warning(f"Failed to destroy camera handle: {ret}")
            
            # Finalize SDK
            MvCamera.MV_CC_Finalize()
            
            self.is_connected = False
            self.logger.info("Camera disconnected")
            return True
            
        except Exception as e:
            self.logger.error(f"Error disconnecting camera: {e}")
            return False
    
    def _configure_camera(self) -> bool:
        """
        Configure camera parameters.
        
        Returns:
            True if configuration successful, False otherwise
        """
        try:
            # Set trigger mode
            trigger_mode = self.config.get('trigger_mode', 'continuous')
            if trigger_mode == 'software':
                ret = self.camera.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_ON)
                if ret != MV_OK:
                    self.logger.error(f"Failed to set trigger mode: {ret}")
                    return False
                
                ret = self.camera.MV_CC_SetEnumValue("TriggerSource", MV_TRIGGER_SOURCE_SOFTWARE)
                if ret != MV_OK:
                    self.logger.error(f"Failed to set trigger source: {ret}")
                    return False
            else:
                ret = self.camera.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
                if ret != MV_OK:
                    self.logger.error(f"Failed to disable trigger mode: {ret}")
                    return False
            
            # Set acquisition mode
            acquisition_mode = self.config.get('acquisition_mode', 'continuous')
            if acquisition_mode == 'continuous':
                ret = self.camera.MV_CC_SetEnumValue("AcquisitionMode", MV_ACQ_MODE_CONTINUOUS)
            else:
                ret = self.camera.MV_CC_SetEnumValue("AcquisitionMode", MV_ACQ_MODE_SINGLE)
            
            if ret != MV_OK:
                self.logger.error(f"Failed to set acquisition mode: {ret}")
                return False
            
            # Set frame rate
            frame_rate = self.config.get('frame_rate', 30)
            ret = self.camera.MV_CC_SetFloatValue("AcquisitionFrameRate", frame_rate)
            if ret != MV_OK:
                self.logger.warning(f"Failed to set frame rate: {ret}")
            
            # Set exposure time
            exposure_time = self.config.get('exposure_time', 10000)
            ret = self.camera.MV_CC_SetFloatValue("ExposureTime", exposure_time)
            if ret != MV_OK:
                self.logger.warning(f"Failed to set exposure time: {ret}")
            
            # Set gain
            gain = self.config.get('gain', 1.0)
            ret = self.camera.MV_CC_SetFloatValue("Gain", gain)
            if ret != MV_OK:
                self.logger.warning(f"Failed to set gain: {ret}")
            
            self.logger.info("Camera configuration completed")
            return True

        except Exception as e:
            self.logger.error(f"Error configuring camera: {e}")
            return False

    def start_grabbing(self) -> bool:
        """
        Start image grabbing.

        Returns:
            True if start successful, False otherwise
        """
        try:
            if not self.is_connected:
                self.logger.error("Camera not connected")
                return False

            if self.is_grabbing:
                self.logger.warning("Camera already grabbing")
                return True

            # Start grabbing
            ret = self.camera.MV_CC_StartGrabbing()
            if ret != MV_OK:
                self.logger.error(f"Failed to start grabbing: {ret}")
                return False

            # Start grab thread
            self._stop_event.clear()
            self._grab_thread = threading.Thread(target=self._grab_loop, daemon=True)
            self._grab_thread.start()

            self.is_grabbing = True
            self.fps_counter.reset()
            self.frame_counter.reset()
            self.error_counter.reset()

            self.logger.info("Started image grabbing")
            return True

        except Exception as e:
            self.logger.error(f"Error starting grabbing: {e}")
            return False

    def stop_grabbing(self) -> bool:
        """
        Stop image grabbing.

        Returns:
            True if stop successful, False otherwise
        """
        try:
            if not self.is_grabbing:
                return True

            # Signal stop
            self._stop_event.set()

            # Wait for thread to finish
            if self._grab_thread and self._grab_thread.is_alive():
                self._grab_thread.join(timeout=2.0)

            # Stop grabbing
            ret = self.camera.MV_CC_StopGrabbing()
            if ret != MV_OK:
                self.logger.warning(f"Failed to stop grabbing: {ret}")

            self.is_grabbing = False
            self.logger.info("Stopped image grabbing")
            return True

        except Exception as e:
            self.logger.error(f"Error stopping grabbing: {e}")
            return False

    def _grab_loop(self) -> None:
        """Main grab loop running in separate thread."""
        self.logger.debug("Grab loop started")

        while not self._stop_event.is_set():
            try:
                # Get one frame
                frame_data = self._get_one_frame()
                if frame_data is not None:
                    # Update statistics
                    self.frame_counter.increment()
                    fps = self.fps_counter.update()

                    # Add to buffer
                    self.frame_buffer.put(frame_data)

                    # Add to queue (non-blocking)
                    try:
                        self._frame_queue.put_nowait({
                            'image': frame_data,
                            'timestamp': time.time(),
                            'frame_id': self.frame_counter.get(),
                            'fps': fps
                        })
                    except queue.Full:
                        # Remove oldest frame and add new one
                        try:
                            self._frame_queue.get_nowait()
                            self._frame_queue.put_nowait({
                                'image': frame_data,
                                'timestamp': time.time(),
                                'frame_id': self.frame_counter.get(),
                                'fps': fps
                            })
                        except queue.Empty:
                            pass

                    # Call frame callback if set
                    if self.frame_callback:
                        try:
                            self.frame_callback(frame_data, self.frame_counter.get(), fps)
                        except Exception as e:
                            self.logger.error(f"Error in frame callback: {e}")

                else:
                    # No frame received, small delay
                    time.sleep(0.001)

            except Exception as e:
                self.error_counter.increment()
                self.logger.error(f"Error in grab loop: {e}")

                # Call error callback if set
                if self.error_callback:
                    try:
                        self.error_callback(e)
                    except Exception as cb_error:
                        self.logger.error(f"Error in error callback: {cb_error}")

                # Small delay before retry
                time.sleep(0.01)

        self.logger.debug("Grab loop stopped")

    def _get_one_frame(self) -> Optional[np.ndarray]:
        """
        Get one frame from camera.

        Returns:
            Image as numpy array or None if failed
        """
        try:
            # Create frame info structure
            frame_info = MV_FRAME_OUT_INFO_EX()
            memset(byref(frame_info), 0, sizeof(frame_info))

            # Get one frame
            ret = self.camera.MV_CC_GetOneFrameTimeout(byref(frame_info), 1000)  # 1 second timeout
            if ret != MV_OK:
                if ret != MV_E_NODATA:  # Don't log timeout errors
                    self.logger.debug(f"Failed to get frame: {ret}")
                return None

            # Convert frame data to numpy array
            image_data = (c_ubyte * frame_info.nFrameLen)()
            memmove(byref(image_data), frame_info.pBufAddr, frame_info.nFrameLen)

            # Convert to numpy array
            if frame_info.enPixelType == PixelType_Gvsp_Mono8:
                # Grayscale image
                image = np.frombuffer(image_data, dtype=np.uint8)
                image = image.reshape((frame_info.nHeight, frame_info.nWidth))
                # Convert to BGR for consistency
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

            elif frame_info.enPixelType in [PixelType_Gvsp_RGB8_Packed, PixelType_Gvsp_BGR8_Packed]:
                # RGB/BGR image
                image = np.frombuffer(image_data, dtype=np.uint8)
                image = image.reshape((frame_info.nHeight, frame_info.nWidth, 3))

                if frame_info.enPixelType == PixelType_Gvsp_RGB8_Packed:
                    # Convert RGB to BGR
                    image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            else:
                # Other pixel formats - try to convert
                self.logger.warning(f"Unsupported pixel format: {frame_info.enPixelType}")
                return None

            return image

        except Exception as e:
            self.logger.error(f"Error getting frame: {e}")
            return None

    def get_latest_frame(self) -> Optional[Dict[str, Any]]:
        """
        Get latest frame from queue.

        Returns:
            Frame data dictionary or None if no frame available
        """
        try:
            return self._frame_queue.get_nowait()
        except queue.Empty:
            return None

    def get_frame_buffer(self, count: int = 1) -> List[np.ndarray]:
        """
        Get frames from buffer.

        Args:
            count: Number of frames to get

        Returns:
            List of frames
        """
        return self.frame_buffer.get_latest(count)

    def trigger_software(self) -> bool:
        """
        Send software trigger command.

        Returns:
            True if trigger successful, False otherwise
        """
        try:
            if not self.is_connected:
                self.logger.error("Camera not connected")
                return False

            ret = self.camera.MV_CC_SetCommandValue("TriggerSoftware")
            if ret != MV_OK:
                self.logger.error(f"Failed to send software trigger: {ret}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error sending software trigger: {e}")
            return False

    def set_parameter(self, param_name: str, value: Any) -> bool:
        """
        Set camera parameter.

        Args:
            param_name: Parameter name
            value: Parameter value

        Returns:
            True if set successful, False otherwise
        """
        try:
            if not self.is_connected:
                self.logger.error("Camera not connected")
                return False

            if isinstance(value, (int, bool)):
                ret = self.camera.MV_CC_SetIntValue(param_name, int(value))
            elif isinstance(value, float):
                ret = self.camera.MV_CC_SetFloatValue(param_name, value)
            elif isinstance(value, str):
                ret = self.camera.MV_CC_SetStringValue(param_name, value)
            else:
                self.logger.error(f"Unsupported parameter type: {type(value)}")
                return False

            if ret != MV_OK:
                self.logger.error(f"Failed to set parameter {param_name}: {ret}")
                return False

            self.logger.debug(f"Set parameter {param_name} = {value}")
            return True

        except Exception as e:
            self.logger.error(f"Error setting parameter {param_name}: {e}")
            return False

    def get_parameter(self, param_name: str, param_type: str = 'int') -> Optional[Any]:
        """
        Get camera parameter.

        Args:
            param_name: Parameter name
            param_type: Parameter type ('int', 'float', 'string')

        Returns:
            Parameter value or None if failed
        """
        try:
            if not self.is_connected:
                self.logger.error("Camera not connected")
                return None

            if param_type == 'int':
                value = MVCC_INTVALUE()
                ret = self.camera.MV_CC_GetIntValue(param_name, value)
                if ret == MV_OK:
                    return value.nCurValue
            elif param_type == 'float':
                value = MVCC_FLOATVALUE()
                ret = self.camera.MV_CC_GetFloatValue(param_name, value)
                if ret == MV_OK:
                    return value.fCurValue
            elif param_type == 'string':
                value = MVCC_STRINGVALUE()
                ret = self.camera.MV_CC_GetStringValue(param_name, value)
                if ret == MV_OK:
                    return value.chCurValue.decode('ascii')
            else:
                self.logger.error(f"Unsupported parameter type: {param_type}")
                return None

            if ret != MV_OK:
                self.logger.error(f"Failed to get parameter {param_name}: {ret}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting parameter {param_name}: {e}")
            return None

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get camera statistics.

        Returns:
            Statistics dictionary
        """
        return {
            'is_connected': self.is_connected,
            'is_grabbing': self.is_grabbing,
            'frame_count': self.frame_counter.get(),
            'error_count': self.error_counter.get(),
            'current_fps': self.fps_counter.update() if self.is_grabbing else 0.0,
            'queue_size': self._frame_queue.qsize(),
            'buffer_size': self.frame_buffer.count
        }

    def set_frame_callback(self, callback: Callable) -> None:
        """
        Set frame callback function.

        Args:
            callback: Callback function(image, frame_id, fps)
        """
        self.frame_callback = callback

    def set_error_callback(self, callback: Callable) -> None:
        """
        Set error callback function.

        Args:
            callback: Callback function(error)
        """
        self.error_callback = callback

    def __del__(self):
        """Destructor to ensure proper cleanup."""
        try:
            self.disconnect()
        except:
            pass

"""
Main Window for YOLO Camera System
PyQt6-based GUI for camera control, detection, and annotation.
"""

import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
import numpy as np
import cv2

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGridLayout, QLabel, QPushButton, QComboBox, QSpinBox, QDoubleSpinBox,
    QSlider, QTextEdit, QTabWidget, QGroupBox, QCheckBox, QProgressBar,
    QFileDialog, QMessageBox, QSplitter, QFrame, QScrollArea
)
from PyQt6.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QMutex, QMutexLocker
)
from PyQt6.QtGui import QPixmap, QImage, QFont, QAction, QIcon

from ..config import config_manager
from ..camera import <PERSON><PERSON><PERSON><PERSON>er
from ..yolo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ..annotation import <PERSON>ota<PERSON><PERSON><PERSON>
from ..utils import <PERSON><PERSON><PERSON>ou<PERSON>, ensure_dir
from ..logger import LoggerMixin, setup_logger


class ImageDisplayWidget(QLabel):
    """Custom widget for displaying images with mouse interaction."""
    
    # Signals
    mouse_clicked = pyqtSignal(int, int)  # x, y coordinates
    mouse_moved = pyqtSignal(int, int)    # x, y coordinates
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(640, 480)
        self.setStyleSheet("border: 1px solid gray;")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setText("No Image")
        
        # Image data
        self.current_image = None
        self.scale_factor = 1.0
        
        # Mouse tracking
        self.setMouseTracking(True)
    
    def set_image(self, image: np.ndarray) -> None:
        """
        Set image to display.
        
        Args:
            image: Image as numpy array (BGR format)
        """
        if image is None:
            self.setText("No Image")
            self.current_image = None
            return
        
        self.current_image = image.copy()
        
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Convert to QImage
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
        
        # Scale image to fit widget
        widget_size = self.size()
        scaled_pixmap = QPixmap.fromImage(qt_image).scaled(
            widget_size, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation
        )
        
        # Calculate scale factor
        self.scale_factor = min(widget_size.width() / w, widget_size.height() / h)
        
        self.setPixmap(scaled_pixmap)
    
    def mousePressEvent(self, event):
        """Handle mouse press events."""
        if self.current_image is not None and event.button() == Qt.MouseButton.LeftButton:
            # Convert widget coordinates to image coordinates
            x, y = self._widget_to_image_coords(event.position().x(), event.position().y())
            if x is not None and y is not None:
                self.mouse_clicked.emit(int(x), int(y))
    
    def mouseMoveEvent(self, event):
        """Handle mouse move events."""
        if self.current_image is not None:
            # Convert widget coordinates to image coordinates
            x, y = self._widget_to_image_coords(event.position().x(), event.position().y())
            if x is not None and y is not None:
                self.mouse_moved.emit(int(x), int(y))
    
    def _widget_to_image_coords(self, widget_x: float, widget_y: float) -> tuple:
        """Convert widget coordinates to image coordinates."""
        if self.current_image is None or self.scale_factor == 0:
            return None, None
        
        # Get image dimensions
        img_h, img_w = self.current_image.shape[:2]
        
        # Get widget dimensions
        widget_size = self.size()
        
        # Calculate scaled image size
        scaled_w = int(img_w * self.scale_factor)
        scaled_h = int(img_h * self.scale_factor)
        
        # Calculate offset (image is centered)
        offset_x = (widget_size.width() - scaled_w) // 2
        offset_y = (widget_size.height() - scaled_h) // 2
        
        # Convert to image coordinates
        img_x = (widget_x - offset_x) / self.scale_factor
        img_y = (widget_y - offset_y) / self.scale_factor
        
        # Check bounds
        if 0 <= img_x < img_w and 0 <= img_y < img_h:
            return img_x, img_y
        else:
            return None, None


class CameraWorker(QThread):
    """Worker thread for camera operations."""
    
    # Signals
    frame_ready = pyqtSignal(np.ndarray, dict)  # image, metadata
    error_occurred = pyqtSignal(str)  # error message
    
    def __init__(self, camera_controller: CameraController):
        super().__init__()
        self.camera_controller = camera_controller
        self.running = False
        self.mutex = QMutex()
    
    def start_capture(self):
        """Start camera capture."""
        with QMutexLocker(self.mutex):
            self.running = True
        self.start()
    
    def stop_capture(self):
        """Stop camera capture."""
        with QMutexLocker(self.mutex):
            self.running = False
        self.wait()
    
    def run(self):
        """Main thread loop."""
        while True:
            with QMutexLocker(self.mutex):
                if not self.running:
                    break
            
            try:
                # Get latest frame
                frame_data = self.camera_controller.get_latest_frame()
                if frame_data:
                    self.frame_ready.emit(frame_data['image'], {
                        'timestamp': frame_data['timestamp'],
                        'frame_id': frame_data['frame_id'],
                        'fps': frame_data['fps']
                    })
                else:
                    self.msleep(10)  # Small delay if no frame
                    
            except Exception as e:
                self.error_occurred.emit(str(e))
                self.msleep(100)  # Longer delay on error


class MainWindow(QMainWindow, LoggerMixin):
    """Main application window."""
    
    def __init__(self):
        super().__init__()
        
        # Load configuration
        self.config = config_manager
        
        # Initialize components
        self.camera_controller = None
        self.yolo_detector = None
        self.annotation_tool = None
        self.camera_worker = None
        
        # UI state
        self.current_image = None
        self.current_detections = []
        self.annotation_mode = False
        
        # Performance tracking
        self.fps_counter = FPSCounter()
        
        # Setup UI
        self.setup_ui()
        self.setup_connections()
        
        # Initialize components
        self.initialize_components()
        
        self.logger.info("Main window initialized")
    
    def setup_ui(self):
        """Setup the user interface."""
        self.setWindowTitle("YOLO Camera System")
        self.setGeometry(100, 100, 1400, 900)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Image display
        self.setup_image_panel(splitter)
        
        # Right panel - Controls
        self.setup_control_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([800, 600])
        
        # Setup menu bar
        self.setup_menu_bar()
        
        # Setup status bar
        self.setup_status_bar()
    
    def setup_image_panel(self, parent):
        """Setup image display panel."""
        image_widget = QWidget()
        image_layout = QVBoxLayout(image_widget)
        
        # Image display
        self.image_display = ImageDisplayWidget()
        image_layout.addWidget(self.image_display)
        
        # Image controls
        image_controls = QHBoxLayout()
        
        self.save_image_btn = QPushButton("Save Image")
        self.save_annotation_btn = QPushButton("Save Annotation")
        self.clear_annotations_btn = QPushButton("Clear Annotations")
        
        image_controls.addWidget(self.save_image_btn)
        image_controls.addWidget(self.save_annotation_btn)
        image_controls.addWidget(self.clear_annotations_btn)
        image_controls.addStretch()
        
        image_layout.addLayout(image_controls)
        
        parent.addWidget(image_widget)
    
    def setup_control_panel(self, parent):
        """Setup control panel with tabs."""
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Camera tab
        self.setup_camera_tab()
        
        # Detection tab
        self.setup_detection_tab()
        
        # Annotation tab
        self.setup_annotation_tab()
        
        # Training tab
        self.setup_training_tab()
        
        # Settings tab
        self.setup_settings_tab()
        
        parent.addWidget(self.tab_widget)
    
    def setup_camera_tab(self):
        """Setup camera control tab."""
        camera_widget = QWidget()
        layout = QVBoxLayout(camera_widget)
        
        # Camera connection group
        connection_group = QGroupBox("Camera Connection")
        connection_layout = QGridLayout(connection_group)
        
        self.camera_combo = QComboBox()
        self.refresh_cameras_btn = QPushButton("Refresh")
        self.connect_btn = QPushButton("Connect")
        self.disconnect_btn = QPushButton("Disconnect")
        
        connection_layout.addWidget(QLabel("Camera:"), 0, 0)
        connection_layout.addWidget(self.camera_combo, 0, 1)
        connection_layout.addWidget(self.refresh_cameras_btn, 0, 2)
        connection_layout.addWidget(self.connect_btn, 1, 0)
        connection_layout.addWidget(self.disconnect_btn, 1, 1)
        
        layout.addWidget(connection_group)
        
        # Camera control group
        control_group = QGroupBox("Camera Control")
        control_layout = QGridLayout(control_group)
        
        self.start_capture_btn = QPushButton("Start Capture")
        self.stop_capture_btn = QPushButton("Stop Capture")
        self.trigger_btn = QPushButton("Software Trigger")
        
        # Camera parameters
        self.exposure_spin = QSpinBox()
        self.exposure_spin.setRange(100, 100000)
        self.exposure_spin.setValue(10000)
        self.exposure_spin.setSuffix(" μs")
        
        self.gain_spin = QDoubleSpinBox()
        self.gain_spin.setRange(0.0, 10.0)
        self.gain_spin.setValue(1.0)
        self.gain_spin.setSingleStep(0.1)
        
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(1, 120)
        self.fps_spin.setValue(30)
        
        control_layout.addWidget(self.start_capture_btn, 0, 0)
        control_layout.addWidget(self.stop_capture_btn, 0, 1)
        control_layout.addWidget(self.trigger_btn, 1, 0, 1, 2)
        
        control_layout.addWidget(QLabel("Exposure:"), 2, 0)
        control_layout.addWidget(self.exposure_spin, 2, 1)
        control_layout.addWidget(QLabel("Gain:"), 3, 0)
        control_layout.addWidget(self.gain_spin, 3, 1)
        control_layout.addWidget(QLabel("FPS:"), 4, 0)
        control_layout.addWidget(self.fps_spin, 4, 1)
        
        layout.addWidget(control_group)
        
        # Camera status group
        status_group = QGroupBox("Camera Status")
        status_layout = QGridLayout(status_group)
        
        self.camera_status_label = QLabel("Disconnected")
        self.frame_count_label = QLabel("Frames: 0")
        self.fps_label = QLabel("FPS: 0.0")
        self.queue_size_label = QLabel("Queue: 0")
        
        status_layout.addWidget(QLabel("Status:"), 0, 0)
        status_layout.addWidget(self.camera_status_label, 0, 1)
        status_layout.addWidget(self.frame_count_label, 1, 0, 1, 2)
        status_layout.addWidget(self.fps_label, 2, 0, 1, 2)
        status_layout.addWidget(self.queue_size_label, 3, 0, 1, 2)
        
        layout.addWidget(status_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(camera_widget, "Camera")
    
    def setup_detection_tab(self):
        """Setup detection control tab."""
        detection_widget = QWidget()
        layout = QVBoxLayout(detection_widget)
        
        # Model group
        model_group = QGroupBox("YOLO Model")
        model_layout = QGridLayout(model_group)
        
        self.model_path_label = QLabel("No model loaded")
        self.load_model_btn = QPushButton("Load Model")
        self.model_info_btn = QPushButton("Model Info")
        
        model_layout.addWidget(QLabel("Model:"), 0, 0)
        model_layout.addWidget(self.model_path_label, 0, 1)
        model_layout.addWidget(self.load_model_btn, 1, 0)
        model_layout.addWidget(self.model_info_btn, 1, 1)
        
        layout.addWidget(model_group)
        
        # Detection parameters group
        params_group = QGroupBox("Detection Parameters")
        params_layout = QGridLayout(params_group)
        
        self.confidence_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_slider.setRange(1, 100)
        self.confidence_slider.setValue(50)
        self.confidence_label = QLabel("0.50")
        
        self.iou_slider = QSlider(Qt.Orientation.Horizontal)
        self.iou_slider.setRange(1, 100)
        self.iou_slider.setValue(45)
        self.iou_label = QLabel("0.45")
        
        self.max_det_spin = QSpinBox()
        self.max_det_spin.setRange(1, 1000)
        self.max_det_spin.setValue(100)
        
        params_layout.addWidget(QLabel("Confidence:"), 0, 0)
        params_layout.addWidget(self.confidence_slider, 0, 1)
        params_layout.addWidget(self.confidence_label, 0, 2)
        
        params_layout.addWidget(QLabel("IoU:"), 1, 0)
        params_layout.addWidget(self.iou_slider, 1, 1)
        params_layout.addWidget(self.iou_label, 1, 2)
        
        params_layout.addWidget(QLabel("Max Detections:"), 2, 0)
        params_layout.addWidget(self.max_det_spin, 2, 1)
        
        layout.addWidget(params_group)
        
        # Detection control group
        detection_control_group = QGroupBox("Detection Control")
        detection_control_layout = QVBoxLayout(detection_control_group)
        
        self.enable_detection_cb = QCheckBox("Enable Real-time Detection")
        self.detect_current_btn = QPushButton("Detect Current Image")
        self.save_detection_btn = QPushButton("Save Detection Result")
        
        detection_control_layout.addWidget(self.enable_detection_cb)
        detection_control_layout.addWidget(self.detect_current_btn)
        detection_control_layout.addWidget(self.save_detection_btn)
        
        layout.addWidget(detection_control_group)
        
        # Detection results
        results_group = QGroupBox("Detection Results")
        results_layout = QVBoxLayout(results_group)
        
        self.detection_results_text = QTextEdit()
        self.detection_results_text.setMaximumHeight(150)
        self.detection_results_text.setReadOnly(True)
        
        results_layout.addWidget(self.detection_results_text)
        
        layout.addWidget(results_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(detection_widget, "Detection")

    def setup_annotation_tab(self):
        """Setup annotation control tab."""
        annotation_widget = QWidget()
        layout = QVBoxLayout(annotation_widget)

        # Annotation mode group
        mode_group = QGroupBox("Annotation Mode")
        mode_layout = QVBoxLayout(mode_group)

        self.annotation_mode_cb = QCheckBox("Enable Annotation Mode")
        self.load_classes_btn = QPushButton("Load Classes")
        self.classes_label = QLabel("No classes loaded")

        mode_layout.addWidget(self.annotation_mode_cb)
        mode_layout.addWidget(self.load_classes_btn)
        mode_layout.addWidget(self.classes_label)

        layout.addWidget(mode_group)

        # Current annotation group
        current_group = QGroupBox("Current Annotation")
        current_layout = QGridLayout(current_group)

        self.class_combo = QComboBox()
        self.bbox_width_spin = QSpinBox()
        self.bbox_width_spin.setRange(10, 1000)
        self.bbox_width_spin.setValue(100)

        self.bbox_height_spin = QSpinBox()
        self.bbox_height_spin.setRange(10, 1000)
        self.bbox_height_spin.setValue(100)

        self.bbox_angle_spin = QSpinBox()
        self.bbox_angle_spin.setRange(-180, 180)
        self.bbox_angle_spin.setValue(0)
        self.bbox_angle_spin.setSuffix("°")

        current_layout.addWidget(QLabel("Class:"), 0, 0)
        current_layout.addWidget(self.class_combo, 0, 1)
        current_layout.addWidget(QLabel("Width:"), 1, 0)
        current_layout.addWidget(self.bbox_width_spin, 1, 1)
        current_layout.addWidget(QLabel("Height:"), 2, 0)
        current_layout.addWidget(self.bbox_height_spin, 2, 1)
        current_layout.addWidget(QLabel("Angle:"), 3, 0)
        current_layout.addWidget(self.bbox_angle_spin, 3, 1)

        layout.addWidget(current_group)

        # Annotation list
        list_group = QGroupBox("Annotations")
        list_layout = QVBoxLayout(list_group)

        self.annotations_text = QTextEdit()
        self.annotations_text.setMaximumHeight(150)
        self.annotations_text.setReadOnly(True)

        annotation_buttons = QHBoxLayout()
        self.delete_annotation_btn = QPushButton("Delete Selected")
        self.clear_all_annotations_btn = QPushButton("Clear All")

        annotation_buttons.addWidget(self.delete_annotation_btn)
        annotation_buttons.addWidget(self.clear_all_annotations_btn)

        list_layout.addWidget(self.annotations_text)
        list_layout.addLayout(annotation_buttons)

        layout.addWidget(list_group)

        layout.addStretch()

        self.tab_widget.addTab(annotation_widget, "Annotation")

    def setup_training_tab(self):
        """Setup training control tab."""
        training_widget = QWidget()
        layout = QVBoxLayout(training_widget)

        # Dataset group
        dataset_group = QGroupBox("Dataset")
        dataset_layout = QGridLayout(dataset_group)

        self.dataset_path_label = QLabel("No dataset selected")
        self.select_dataset_btn = QPushButton("Select Dataset")
        self.export_dataset_btn = QPushButton("Export Dataset")

        dataset_layout.addWidget(QLabel("Dataset:"), 0, 0)
        dataset_layout.addWidget(self.dataset_path_label, 0, 1)
        dataset_layout.addWidget(self.select_dataset_btn, 1, 0)
        dataset_layout.addWidget(self.export_dataset_btn, 1, 1)

        layout.addWidget(dataset_group)

        # Training parameters group
        training_params_group = QGroupBox("Training Parameters")
        training_params_layout = QGridLayout(training_params_group)

        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(1, 1000)
        self.epochs_spin.setValue(10)

        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 64)
        self.batch_size_spin.setValue(16)

        self.learning_rate_spin = QDoubleSpinBox()
        self.learning_rate_spin.setRange(0.0001, 1.0)
        self.learning_rate_spin.setValue(0.001)
        self.learning_rate_spin.setDecimals(4)
        self.learning_rate_spin.setSingleStep(0.0001)

        training_params_layout.addWidget(QLabel("Epochs:"), 0, 0)
        training_params_layout.addWidget(self.epochs_spin, 0, 1)
        training_params_layout.addWidget(QLabel("Batch Size:"), 1, 0)
        training_params_layout.addWidget(self.batch_size_spin, 1, 1)
        training_params_layout.addWidget(QLabel("Learning Rate:"), 2, 0)
        training_params_layout.addWidget(self.learning_rate_spin, 2, 1)

        layout.addWidget(training_params_group)

        # Training control group
        training_control_group = QGroupBox("Training Control")
        training_control_layout = QVBoxLayout(training_control_group)

        self.start_training_btn = QPushButton("Start Incremental Training")
        self.stop_training_btn = QPushButton("Stop Training")
        self.training_progress = QProgressBar()

        training_control_layout.addWidget(self.start_training_btn)
        training_control_layout.addWidget(self.stop_training_btn)
        training_control_layout.addWidget(self.training_progress)

        layout.addWidget(training_control_group)

        # Training history group
        history_group = QGroupBox("Training History")
        history_layout = QVBoxLayout(history_group)

        self.training_history_text = QTextEdit()
        self.training_history_text.setMaximumHeight(150)
        self.training_history_text.setReadOnly(True)

        history_buttons = QHBoxLayout()
        self.load_history_btn = QPushButton("Load History")
        self.rollback_btn = QPushButton("Rollback to Version")

        history_buttons.addWidget(self.load_history_btn)
        history_buttons.addWidget(self.rollback_btn)

        history_layout.addWidget(self.training_history_text)
        history_layout.addLayout(history_buttons)

        layout.addWidget(history_group)

        layout.addStretch()

        self.tab_widget.addTab(training_widget, "Training")

    def setup_settings_tab(self):
        """Setup settings tab."""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)

        # General settings group
        general_group = QGroupBox("General Settings")
        general_layout = QGridLayout(general_group)

        self.auto_save_cb = QCheckBox("Auto-save annotations")
        self.auto_save_cb.setChecked(True)

        self.show_fps_cb = QCheckBox("Show FPS counter")
        self.show_fps_cb.setChecked(True)

        self.gpu_acceleration_cb = QCheckBox("Enable GPU acceleration")

        general_layout.addWidget(self.auto_save_cb, 0, 0)
        general_layout.addWidget(self.show_fps_cb, 1, 0)
        general_layout.addWidget(self.gpu_acceleration_cb, 2, 0)

        layout.addWidget(general_group)

        # Paths group
        paths_group = QGroupBox("Paths")
        paths_layout = QGridLayout(paths_group)

        self.images_path_label = QLabel("data/images")
        self.annotations_path_label = QLabel("data/annotations")
        self.models_path_label = QLabel("models")

        self.select_images_path_btn = QPushButton("Select")
        self.select_annotations_path_btn = QPushButton("Select")
        self.select_models_path_btn = QPushButton("Select")

        paths_layout.addWidget(QLabel("Images:"), 0, 0)
        paths_layout.addWidget(self.images_path_label, 0, 1)
        paths_layout.addWidget(self.select_images_path_btn, 0, 2)

        paths_layout.addWidget(QLabel("Annotations:"), 1, 0)
        paths_layout.addWidget(self.annotations_path_label, 1, 1)
        paths_layout.addWidget(self.select_annotations_path_btn, 1, 2)

        paths_layout.addWidget(QLabel("Models:"), 2, 0)
        paths_layout.addWidget(self.models_path_label, 2, 1)
        paths_layout.addWidget(self.select_models_path_btn, 2, 2)

        layout.addWidget(paths_group)

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout(config_group)

        config_buttons = QHBoxLayout()
        self.save_config_btn = QPushButton("Save Configuration")
        self.load_config_btn = QPushButton("Load Configuration")
        self.reset_config_btn = QPushButton("Reset to Default")

        config_buttons.addWidget(self.save_config_btn)
        config_buttons.addWidget(self.load_config_btn)
        config_buttons.addWidget(self.reset_config_btn)

        config_layout.addLayout(config_buttons)

        layout.addWidget(config_group)

        layout.addStretch()

        self.tab_widget.addTab(settings_widget, "Settings")

    def setup_menu_bar(self):
        """Setup menu bar."""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('File')

        open_image_action = QAction('Open Image', self)
        open_image_action.triggered.connect(self.open_image)
        file_menu.addAction(open_image_action)

        save_image_action = QAction('Save Image', self)
        save_image_action.triggered.connect(self.save_current_image)
        file_menu.addAction(save_image_action)

        file_menu.addSeparator()

        exit_action = QAction('Exit', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # View menu
        view_menu = menubar.addMenu('View')

        toggle_annotations_action = QAction('Toggle Annotations', self)
        toggle_annotations_action.triggered.connect(self.toggle_annotation_display)
        view_menu.addAction(toggle_annotations_action)

        # Help menu
        help_menu = menubar.addMenu('Help')

        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """Setup status bar."""
        self.status_bar = self.statusBar()

        # Status labels
        self.status_label = QLabel("Ready")
        self.fps_status_label = QLabel("FPS: 0.0")
        self.detection_status_label = QLabel("Detections: 0")

        self.status_bar.addWidget(self.status_label)
        self.status_bar.addPermanentWidget(self.detection_status_label)
        self.status_bar.addPermanentWidget(self.fps_status_label)

    def setup_connections(self):
        """Setup signal-slot connections."""
        # Camera connections
        self.refresh_cameras_btn.clicked.connect(self.refresh_cameras)
        self.connect_btn.clicked.connect(self.connect_camera)
        self.disconnect_btn.clicked.connect(self.disconnect_camera)
        self.start_capture_btn.clicked.connect(self.start_capture)
        self.stop_capture_btn.clicked.connect(self.stop_capture)
        self.trigger_btn.clicked.connect(self.software_trigger)

        # Parameter connections
        self.exposure_spin.valueChanged.connect(self.update_camera_parameters)
        self.gain_spin.valueChanged.connect(self.update_camera_parameters)
        self.fps_spin.valueChanged.connect(self.update_camera_parameters)

        # Detection connections
        self.load_model_btn.clicked.connect(self.load_yolo_model)
        self.model_info_btn.clicked.connect(self.show_model_info)
        self.confidence_slider.valueChanged.connect(self.update_detection_parameters)
        self.iou_slider.valueChanged.connect(self.update_detection_parameters)
        self.max_det_spin.valueChanged.connect(self.update_detection_parameters)
        self.enable_detection_cb.toggled.connect(self.toggle_real_time_detection)
        self.detect_current_btn.clicked.connect(self.detect_current_image)

        # Annotation connections
        self.annotation_mode_cb.toggled.connect(self.toggle_annotation_mode)
        self.load_classes_btn.clicked.connect(self.load_annotation_classes)
        self.image_display.mouse_clicked.connect(self.on_image_clicked)

        # Image connections
        self.save_image_btn.clicked.connect(self.save_current_image)
        self.save_annotation_btn.clicked.connect(self.save_current_annotations)
        self.clear_annotations_btn.clicked.connect(self.clear_annotations)

        # Training connections
        self.select_dataset_btn.clicked.connect(self.select_training_dataset)
        self.export_dataset_btn.clicked.connect(self.export_annotation_dataset)
        self.start_training_btn.clicked.connect(self.start_incremental_training)

        # Settings connections
        self.save_config_btn.clicked.connect(self.save_configuration)
        self.load_config_btn.clicked.connect(self.load_configuration)
        self.reset_config_btn.clicked.connect(self.reset_configuration)

        # Timer for UI updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(100)  # Update every 100ms

    def initialize_components(self):
        """Initialize system components."""
        try:
            # Initialize camera controller
            camera_config = self.config.get_all().get('camera', {})
            self.camera_controller = CameraController(camera_config)

            # Initialize YOLO detector
            yolo_config = self.config.get_all().get('yolo', {})
            self.yolo_detector = YOLODetector(yolo_config)

            # Initialize annotation tool
            annotation_config = self.config.get_all().get('annotation', {})
            self.annotation_tool = AnnotationTool(annotation_config)

            # Refresh cameras
            self.refresh_cameras()

            # Load YOLO model if path exists
            model_path = self.config.get('yolo.model_path')
            if model_path and Path(model_path).exists():
                self.yolo_detector.load_model(model_path)
                self.model_path_label.setText(str(Path(model_path).name))

            self.logger.info("Components initialized successfully")

        except Exception as e:
            self.logger.error(f"Error initializing components: {e}")
            QMessageBox.critical(self, "Initialization Error", f"Failed to initialize components:\n{e}")

    def refresh_cameras(self):
        """Refresh available cameras."""
        try:
            self.camera_combo.clear()

            if self.camera_controller:
                devices = self.camera_controller.enumerate_devices()
                for i, device in enumerate(devices):
                    display_name = f"{device.get('manufacturer', 'Unknown')} {device.get('model', 'Camera')} ({device.get('interface_type', 'Unknown')})"
                    self.camera_combo.addItem(display_name, i)

                self.logger.info(f"Found {len(devices)} camera devices")

        except Exception as e:
            self.logger.error(f"Error refreshing cameras: {e}")
            QMessageBox.warning(self, "Camera Error", f"Failed to refresh cameras:\n{e}")

    def connect_camera(self):
        """Connect to selected camera."""
        try:
            if not self.camera_controller:
                QMessageBox.warning(self, "Error", "Camera controller not initialized")
                return

            device_index = self.camera_combo.currentData()
            if device_index is None:
                QMessageBox.warning(self, "Error", "No camera selected")
                return

            if self.camera_controller.connect(device_index):
                self.camera_status_label.setText("Connected")
                self.connect_btn.setEnabled(False)
                self.disconnect_btn.setEnabled(True)
                self.start_capture_btn.setEnabled(True)
                self.logger.info("Camera connected successfully")
            else:
                QMessageBox.critical(self, "Connection Error", "Failed to connect to camera")

        except Exception as e:
            self.logger.error(f"Error connecting camera: {e}")
            QMessageBox.critical(self, "Connection Error", f"Failed to connect to camera:\n{e}")

    def disconnect_camera(self):
        """Disconnect from camera."""
        try:
            if self.camera_controller and self.camera_controller.is_connected:
                # Stop capture first
                self.stop_capture()

                if self.camera_controller.disconnect():
                    self.camera_status_label.setText("Disconnected")
                    self.connect_btn.setEnabled(True)
                    self.disconnect_btn.setEnabled(False)
                    self.start_capture_btn.setEnabled(False)
                    self.stop_capture_btn.setEnabled(False)
                    self.logger.info("Camera disconnected")

        except Exception as e:
            self.logger.error(f"Error disconnecting camera: {e}")

    def start_capture(self):
        """Start camera capture."""
        try:
            if not self.camera_controller or not self.camera_controller.is_connected:
                QMessageBox.warning(self, "Error", "Camera not connected")
                return

            if self.camera_controller.start_grabbing():
                # Create and start camera worker
                self.camera_worker = CameraWorker(self.camera_controller)
                self.camera_worker.frame_ready.connect(self.on_frame_received)
                self.camera_worker.error_occurred.connect(self.on_camera_error)
                self.camera_worker.start_capture()

                self.start_capture_btn.setEnabled(False)
                self.stop_capture_btn.setEnabled(True)
                self.trigger_btn.setEnabled(True)

                self.logger.info("Camera capture started")
            else:
                QMessageBox.critical(self, "Capture Error", "Failed to start camera capture")

        except Exception as e:
            self.logger.error(f"Error starting capture: {e}")
            QMessageBox.critical(self, "Capture Error", f"Failed to start capture:\n{e}")

    def stop_capture(self):
        """Stop camera capture."""
        try:
            if self.camera_worker:
                self.camera_worker.stop_capture()
                self.camera_worker = None

            if self.camera_controller and self.camera_controller.is_grabbing:
                self.camera_controller.stop_grabbing()

            self.start_capture_btn.setEnabled(True)
            self.stop_capture_btn.setEnabled(False)
            self.trigger_btn.setEnabled(False)

            self.logger.info("Camera capture stopped")

        except Exception as e:
            self.logger.error(f"Error stopping capture: {e}")

    def software_trigger(self):
        """Send software trigger."""
        try:
            if self.camera_controller and self.camera_controller.is_connected:
                self.camera_controller.trigger_software()
                self.logger.debug("Software trigger sent")

        except Exception as e:
            self.logger.error(f"Error sending software trigger: {e}")

    def update_camera_parameters(self):
        """Update camera parameters."""
        try:
            if not self.camera_controller or not self.camera_controller.is_connected:
                return

            # Update exposure
            exposure = self.exposure_spin.value()
            self.camera_controller.set_parameter("ExposureTime", exposure)

            # Update gain
            gain = self.gain_spin.value()
            self.camera_controller.set_parameter("Gain", gain)

            # Update frame rate
            fps = self.fps_spin.value()
            self.camera_controller.set_parameter("AcquisitionFrameRate", fps)

        except Exception as e:
            self.logger.error(f"Error updating camera parameters: {e}")

    def on_frame_received(self, image: np.ndarray, metadata: dict):
        """Handle received frame from camera."""
        try:
            self.current_image = image.copy()

            # Perform detection if enabled
            if self.enable_detection_cb.isChecked() and self.yolo_detector and self.yolo_detector.model:
                detections = self.yolo_detector.detect(image)
                self.current_detections = detections

                # Draw detections on image
                if detections:
                    image = self.yolo_detector.draw_detections(image, detections)
                    self.update_detection_results(detections)

            # Draw annotations if in annotation mode
            if self.annotation_mode and self.annotation_tool and self.annotation_tool.current_annotations:
                image = self.annotation_tool.draw_annotations(image)

            # Update image display
            self.image_display.set_image(image)

            # Update FPS
            fps = metadata.get('fps', 0.0)
            self.fps_counter.update()

        except Exception as e:
            self.logger.error(f"Error processing frame: {e}")

    def on_camera_error(self, error_message: str):
        """Handle camera errors."""
        self.logger.error(f"Camera error: {error_message}")
        self.status_label.setText(f"Camera Error: {error_message}")

    def load_yolo_model(self):
        """Load YOLO model."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Load YOLO Model", "", "Model Files (*.pt *.onnx);;All Files (*)"
            )

            if file_path:
                if self.yolo_detector.load_model(file_path):
                    self.model_path_label.setText(Path(file_path).name)
                    self.logger.info(f"YOLO model loaded: {file_path}")
                    QMessageBox.information(self, "Success", "YOLO model loaded successfully")
                else:
                    QMessageBox.critical(self, "Error", "Failed to load YOLO model")

        except Exception as e:
            self.logger.error(f"Error loading YOLO model: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load YOLO model:\n{e}")

    def show_model_info(self):
        """Show model information."""
        try:
            if self.yolo_detector:
                info = self.yolo_detector.get_model_info()
                info_text = "\n".join([f"{k}: {v}" for k, v in info.items()])
                QMessageBox.information(self, "Model Information", info_text)
            else:
                QMessageBox.warning(self, "Error", "YOLO detector not initialized")

        except Exception as e:
            self.logger.error(f"Error showing model info: {e}")

    def update_detection_parameters(self):
        """Update detection parameters."""
        try:
            if not self.yolo_detector:
                return

            # Update confidence threshold
            confidence = self.confidence_slider.value() / 100.0
            self.confidence_label.setText(f"{confidence:.2f}")
            self.yolo_detector.confidence_threshold = confidence

            # Update IoU threshold
            iou = self.iou_slider.value() / 100.0
            self.iou_label.setText(f"{iou:.2f}")
            self.yolo_detector.iou_threshold = iou

            # Update max detections
            max_det = self.max_det_spin.value()
            self.yolo_detector.max_detections = max_det

        except Exception as e:
            self.logger.error(f"Error updating detection parameters: {e}")

    def toggle_real_time_detection(self, enabled: bool):
        """Toggle real-time detection."""
        self.logger.info(f"Real-time detection {'enabled' if enabled else 'disabled'}")

    def detect_current_image(self):
        """Detect objects in current image."""
        try:
            if not self.current_image is not None:
                QMessageBox.warning(self, "Error", "No image available")
                return

            if not self.yolo_detector or not self.yolo_detector.model:
                QMessageBox.warning(self, "Error", "YOLO model not loaded")
                return

            detections = self.yolo_detector.detect(self.current_image)
            self.current_detections = detections

            # Draw detections
            result_image = self.yolo_detector.draw_detections(self.current_image, detections)
            self.image_display.set_image(result_image)

            # Update results
            self.update_detection_results(detections)

        except Exception as e:
            self.logger.error(f"Error detecting current image: {e}")
            QMessageBox.critical(self, "Detection Error", f"Failed to detect objects:\n{e}")

    def update_detection_results(self, detections: List[Dict]):
        """Update detection results display."""
        try:
            results_text = f"Detected {len(detections)} objects:\n\n"

            for i, detection in enumerate(detections):
                results_text += f"{i+1}. {detection['class_name']}: {detection['confidence']:.2f}\n"
                results_text += f"   BBox: ({detection['bbox'][0]:.1f}, {detection['bbox'][1]:.1f}, "
                results_text += f"{detection['bbox'][2]:.1f}, {detection['bbox'][3]:.1f})\n\n"

            self.detection_results_text.setText(results_text)
            self.detection_status_label.setText(f"Detections: {len(detections)}")

        except Exception as e:
            self.logger.error(f"Error updating detection results: {e}")

    def toggle_annotation_mode(self, enabled: bool):
        """Toggle annotation mode."""
        self.annotation_mode = enabled
        self.logger.info(f"Annotation mode {'enabled' if enabled else 'disabled'}")

    def load_annotation_classes(self):
        """Load annotation classes."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Load Classes File", "", "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                if self.annotation_tool.load_classes(file_path):
                    self.class_combo.clear()
                    self.class_combo.addItems(self.annotation_tool.class_names)
                    self.classes_label.setText(f"{len(self.annotation_tool.class_names)} classes loaded")
                    self.logger.info(f"Loaded {len(self.annotation_tool.class_names)} classes")
                else:
                    QMessageBox.critical(self, "Error", "Failed to load classes file")

        except Exception as e:
            self.logger.error(f"Error loading classes: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load classes:\n{e}")

    def on_image_clicked(self, x: int, y: int):
        """Handle image click for annotation."""
        try:
            if not self.annotation_mode or not self.annotation_tool:
                return

            if not self.current_image is not None:
                return

            # Load current image to annotation tool
            self.annotation_tool.load_image(self.current_image)

            # Get annotation parameters
            class_name = self.class_combo.currentText()
            if not class_name:
                QMessageBox.warning(self, "Error", "No class selected")
                return

            width = self.bbox_width_spin.value()
            height = self.bbox_height_spin.value()
            angle = self.bbox_angle_spin.value()

            # Add annotation
            if self.annotation_tool.add_annotation(x, y, width, height, class_name, angle):
                self.update_annotations_display()

                # Redraw image with annotations
                annotated_image = self.annotation_tool.draw_annotations()
                self.image_display.set_image(annotated_image)

                self.logger.debug(f"Added annotation: {class_name} at ({x}, {y})")

        except Exception as e:
            self.logger.error(f"Error adding annotation: {e}")

    def update_annotations_display(self):
        """Update annotations display."""
        try:
            if not self.annotation_tool:
                return

            annotations_text = f"Annotations ({len(self.annotation_tool.current_annotations)}):\n\n"

            for i, bbox in enumerate(self.annotation_tool.current_annotations):
                annotations_text += f"{i+1}. {bbox.class_name}\n"
                annotations_text += f"   Center: ({bbox.x:.1f}, {bbox.y:.1f})\n"
                annotations_text += f"   Size: {bbox.width:.1f} x {bbox.height:.1f}\n"
                if bbox.angle != 0:
                    annotations_text += f"   Angle: {bbox.angle:.1f}°\n"
                annotations_text += "\n"

            self.annotations_text.setText(annotations_text)

        except Exception as e:
            self.logger.error(f"Error updating annotations display: {e}")

    def save_current_image(self):
        """Save current image."""
        try:
            if self.current_image is None:
                QMessageBox.warning(self, "Error", "No image to save")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save Image", "", "Image Files (*.jpg *.png *.bmp);;All Files (*)"
            )

            if file_path:
                cv2.imwrite(file_path, self.current_image)
                self.logger.info(f"Image saved: {file_path}")
                QMessageBox.information(self, "Success", "Image saved successfully")

        except Exception as e:
            self.logger.error(f"Error saving image: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save image:\n{e}")

    def save_current_annotations(self):
        """Save current annotations."""
        try:
            if not self.annotation_tool or not self.annotation_tool.current_annotations:
                QMessageBox.warning(self, "Error", "No annotations to save")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save Annotations", "", "Text Files (*.txt);;JSON Files (*.json);;All Files (*)"
            )

            if file_path:
                if self.annotation_tool.save_annotations(file_path):
                    self.logger.info(f"Annotations saved: {file_path}")
                    QMessageBox.information(self, "Success", "Annotations saved successfully")
                else:
                    QMessageBox.critical(self, "Error", "Failed to save annotations")

        except Exception as e:
            self.logger.error(f"Error saving annotations: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save annotations:\n{e}")

    def clear_annotations(self):
        """Clear all annotations."""
        try:
            if self.annotation_tool:
                self.annotation_tool.current_annotations.clear()
                self.update_annotations_display()

                # Redraw image without annotations
                if self.current_image is not None:
                    self.image_display.set_image(self.current_image)

                self.logger.info("Annotations cleared")

        except Exception as e:
            self.logger.error(f"Error clearing annotations: {e}")

    def open_image(self):
        """Open image file."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Open Image", "", "Image Files (*.jpg *.jpeg *.png *.bmp);;All Files (*)"
            )

            if file_path:
                image = cv2.imread(file_path)
                if image is not None:
                    self.current_image = image
                    self.image_display.set_image(image)

                    # Load annotations if available
                    if self.annotation_tool:
                        self.annotation_tool.load_image(file_path)
                        self.update_annotations_display()

                    self.logger.info(f"Image opened: {file_path}")
                else:
                    QMessageBox.critical(self, "Error", "Failed to load image")

        except Exception as e:
            self.logger.error(f"Error opening image: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open image:\n{e}")

    def toggle_annotation_display(self):
        """Toggle annotation display."""
        # This would toggle between showing/hiding annotations
        pass

    def show_about(self):
        """Show about dialog."""
        about_text = """
        YOLO Camera System v1.0.0

        A real-time camera capture and object detection system
        using YOLO models with annotation capabilities.

        Features:
        - Real-time camera capture
        - YOLO object detection
        - Image annotation tools
        - Incremental model training
        - Configuration management
        """
        QMessageBox.about(self, "About YOLO Camera System", about_text)

    def select_training_dataset(self):
        """Select training dataset directory."""
        try:
            dir_path = QFileDialog.getExistingDirectory(self, "Select Training Dataset Directory")

            if dir_path:
                self.dataset_path_label.setText(dir_path)
                self.logger.info(f"Training dataset selected: {dir_path}")

        except Exception as e:
            self.logger.error(f"Error selecting dataset: {e}")

    def export_annotation_dataset(self):
        """Export annotation dataset."""
        try:
            if not self.annotation_tool:
                QMessageBox.warning(self, "Error", "Annotation tool not initialized")
                return

            # Get images directory
            images_dir = QFileDialog.getExistingDirectory(self, "Select Images Directory")
            if not images_dir:
                return

            # Get output directory
            output_dir = QFileDialog.getExistingDirectory(self, "Select Output Directory")
            if not output_dir:
                return

            # Export dataset
            if self.annotation_tool.export_dataset(images_dir, output_dir, format='yolo'):
                QMessageBox.information(self, "Success", "Dataset exported successfully")
                self.logger.info(f"Dataset exported to: {output_dir}")
            else:
                QMessageBox.critical(self, "Error", "Failed to export dataset")

        except Exception as e:
            self.logger.error(f"Error exporting dataset: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export dataset:\n{e}")

    def start_incremental_training(self):
        """Start incremental training."""
        try:
            if not self.yolo_detector:
                QMessageBox.warning(self, "Error", "YOLO detector not initialized")
                return

            dataset_path = self.dataset_path_label.text()
            if dataset_path == "No dataset selected":
                QMessageBox.warning(self, "Error", "No dataset selected")
                return

            # Get training parameters
            epochs = self.epochs_spin.value()
            batch_size = self.batch_size_spin.value()
            learning_rate = self.learning_rate_spin.value()

            # Start training (this should be done in a separate thread)
            self.start_training_btn.setEnabled(False)
            self.training_progress.setRange(0, 0)  # Indeterminate progress

            # TODO: Implement training in separate thread
            QMessageBox.information(self, "Training", "Training started (implementation needed)")

        except Exception as e:
            self.logger.error(f"Error starting training: {e}")
            QMessageBox.critical(self, "Error", f"Failed to start training:\n{e}")

    def save_configuration(self):
        """Save current configuration."""
        try:
            self.config.save_user_config()
            QMessageBox.information(self, "Success", "Configuration saved successfully")
            self.logger.info("Configuration saved")

        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save configuration:\n{e}")

    def load_configuration(self):
        """Load configuration from file."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Load Configuration", "", "YAML Files (*.yaml *.yml);;All Files (*)"
            )

            if file_path:
                self.config.import_config(file_path)
                QMessageBox.information(self, "Success", "Configuration loaded successfully")
                self.logger.info(f"Configuration loaded from: {file_path}")

        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load configuration:\n{e}")

    def reset_configuration(self):
        """Reset configuration to default."""
        try:
            reply = QMessageBox.question(
                self, "Reset Configuration",
                "Are you sure you want to reset to default configuration?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.config.reset_to_default()
                QMessageBox.information(self, "Success", "Configuration reset to default")
                self.logger.info("Configuration reset to default")

        except Exception as e:
            self.logger.error(f"Error resetting configuration: {e}")
            QMessageBox.critical(self, "Error", f"Failed to reset configuration:\n{e}")

    def update_ui(self):
        """Update UI elements periodically."""
        try:
            # Update camera status
            if self.camera_controller:
                stats = self.camera_controller.get_statistics()
                self.frame_count_label.setText(f"Frames: {stats['frame_count']}")
                self.fps_label.setText(f"FPS: {stats['current_fps']:.1f}")
                self.queue_size_label.setText(f"Queue: {stats['queue_size']}")

                # Update status bar
                self.fps_status_label.setText(f"FPS: {stats['current_fps']:.1f}")

            # Update detection count in status bar
            if hasattr(self, 'current_detections'):
                self.detection_status_label.setText(f"Detections: {len(self.current_detections)}")

        except Exception as e:
            self.logger.error(f"Error updating UI: {e}")

    def closeEvent(self, event):
        """Handle application close event."""
        try:
            # Stop camera capture
            self.stop_capture()

            # Disconnect camera
            self.disconnect_camera()

            # Save configuration
            self.config.save_user_config()

            self.logger.info("Application closing")
            event.accept()

        except Exception as e:
            self.logger.error(f"Error during application close: {e}")
            event.accept()


def main():
    """Main application entry point."""
    # Setup logging
    setup_logger(
        log_level="INFO",
        log_file="logs/yolo_camera.log",
        console_output=True
    )

    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("YOLO Camera System")
    app.setApplicationVersion("1.0.0")

    # Create and show main window
    window = MainWindow()
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

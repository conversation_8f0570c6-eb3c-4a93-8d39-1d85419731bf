# YOLO Camera System Configuration

# Application Settings
app:
  name: "YOLO Camera System"
  version: "1.0.0"
  debug: false
  log_level: "INFO"

# Camera Configuration
camera:
  # Trigger modes: continuous, software, hardware
  trigger_mode: "continuous"
  # Acquisition modes: single, continuous
  acquisition_mode: "continuous"
  # Image format settings
  pixel_format: "RGB8_Packed"
  # Frame rate (fps)
  frame_rate: 30
  # Exposure time (microseconds)
  exposure_time: 10000
  # Gain value
  gain: 1.0
  # Image buffer size
  buffer_size: 10

# YOLO Model Configuration
yolo:
  # Model file path (relative to project root)
  model_path: "models/yolov11n.pt"
  # Confidence threshold for detection
  confidence_threshold: 0.5
  # IoU threshold for NMS
  iou_threshold: 0.45
  # Maximum number of detections
  max_detections: 100
  # Input image size
  input_size: 640
  # Device: cpu, cuda, mps
  device: "cpu"
  # Classes to detect (empty means all classes)
  classes: []

# Training Configuration
training:
  # Training data directory
  data_dir: "data/training"
  # Validation split ratio
  val_split: 0.2
  # Batch size
  batch_size: 16
  # Number of epochs
  epochs: 100
  # Learning rate
  learning_rate: 0.001
  # Image size for training
  img_size: 640
  # Augmentation settings
  augment: true
  # Save best model only
  save_best_only: true

# Annotation Configuration
annotation:
  # Default annotation format: yolo, coco, pascal_voc
  format: "yolo"
  # Auto-save annotations
  auto_save: true
  # Annotation colors (BGR format)
  colors:
    bbox: [0, 255, 0]  # Green
    text: [255, 255, 255]  # White
    background: [0, 0, 0]  # Black
  # Line thickness
  line_thickness: 2
  # Font scale
  font_scale: 0.5

# Image Storage Configuration
storage:
  # Base directory for saving images
  base_dir: "data/images"
  # Subdirectories
  raw_dir: "raw"
  annotated_dir: "annotated"
  detected_dir: "detected"
  # Image format for saving
  save_format: "jpg"
  # JPEG quality (1-100)
  jpeg_quality: 95
  # Auto-create directories
  auto_create_dirs: true

# Server Configuration
server:
  # Enable TCP server
  enabled: false
  # Server host
  host: "localhost"
  # Server port
  port: 8888
  # Maximum number of clients
  max_clients: 5
  # Send detection results
  send_detections: true
  # Send images
  send_images: false

# UI Configuration
ui:
  # Window size
  window_width: 1200
  window_height: 800
  # Image display size
  display_width: 640
  display_height: 480
  # Update interval (milliseconds)
  update_interval: 33  # ~30 FPS
  # Show FPS counter
  show_fps: true
  # Show detection info
  show_detection_info: true

# Logging Configuration
logging:
  # Log file path
  log_file: "logs/yolo_camera.log"
  # Log rotation
  rotation: "10 MB"
  # Log retention
  retention: "7 days"
  # Console output
  console_output: true
  # Log format
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# Performance Configuration
performance:
  # Number of worker threads
  worker_threads: 4
  # Image processing queue size
  processing_queue_size: 10
  # Enable GPU acceleration
  use_gpu: false
  # Memory optimization
  optimize_memory: true

# YOLO Camera System

A comprehensive real-time camera capture and object detection system using YOLO models with annotation capabilities and incremental training support.

## Features

### 🎥 Camera Control
- **Multi-camera Support**: Compatible with GigE and USB3 cameras via MvImport SDK
- **Flexible Trigger Modes**: Continuous, software, and hardware triggering
- **Real-time Preview**: Live image display with performance monitoring
- **Parameter Control**: Adjustable exposure, gain, frame rate, and other camera settings

### 🤖 YOLO Object Detection
- **YOLOv11 Integration**: Latest YOLO model support with GPU acceleration
- **Real-time Inference**: Live object detection on camera feed
- **Configurable Parameters**: Adjustable confidence threshold, IoU threshold, and detection limits
- **Performance Monitoring**: FPS tracking and inference time measurement

### 🏷️ Image Annotation
- **Interactive Annotation**: Click-to-annotate with bounding boxes
- **Rotation Support**: Rotatable bounding boxes for complex objects
- **Multiple Formats**: YOLO, COCO, and custom JSON annotation formats
- **Class Management**: Customizable object classes with color coding

### 🎯 Incremental Training
- **Model Updates**: Train existing models with new annotated data
- **Version Control**: Track training history and rollback to previous versions
- **Batch Processing**: Efficient training with configurable parameters
- **Performance Tracking**: Monitor training progress and model improvements

### 🖥️ User Interface
- **PyQt6 GUI**: Modern, responsive interface with tabbed layout
- **Real-time Display**: Live camera feed with overlay annotations and detections
- **Control Panels**: Organized controls for camera, detection, annotation, and training
- **Status Monitoring**: Real-time performance metrics and system status

### ⚙️ Configuration Management
- **YAML Configuration**: Human-readable configuration files
- **User Preferences**: Persistent settings with import/export capabilities
- **Default Settings**: Comprehensive default configuration for quick setup

## Installation

### Prerequisites
- Python 3.8 or higher
- Windows 10/11 (for MvImport SDK)
- CUDA-compatible GPU (optional, for acceleration)

### Dependencies Installation
```bash
# Clone the repository
git clone <repository-url>
cd yolo_cam

# Install Python dependencies
pip install -r requirements.txt

# For GPU acceleration (optional)
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### Camera SDK Setup
The MvImport SDK is included in the project. Ensure your camera drivers are properly installed.

## Quick Start

### 1. Launch the Application
```bash
python main.py
```

### 2. Connect Camera
1. Go to the **Camera** tab
2. Click **Refresh** to detect available cameras
3. Select your camera and click **Connect**
4. Click **Start Capture** to begin live preview

### 3. Load YOLO Model
1. Go to the **Detection** tab
2. Click **Load Model** and select a YOLO model file (.pt)
3. Enable **Real-time Detection** for live object detection

### 4. Annotate Images
1. Go to the **Annotation** tab
2. Click **Load Classes** to load object classes
3. Enable **Annotation Mode**
4. Click on images to add bounding box annotations

### 5. Train Model
1. Go to the **Training** tab
2. Select your annotated dataset
3. Configure training parameters
4. Click **Start Incremental Training**

## Configuration

### Camera Settings
```yaml
camera:
  trigger_mode: "continuous"  # continuous, software, hardware
  frame_rate: 30
  exposure_time: 10000  # microseconds
  gain: 1.0
  buffer_size: 10
```

### YOLO Settings
```yaml
yolo:
  model_path: "models/yolov11n.pt"
  confidence_threshold: 0.5
  iou_threshold: 0.45
  device: "cpu"  # cpu, cuda
  input_size: 640
```

### Annotation Settings
```yaml
annotation:
  format: "yolo"  # yolo, coco, custom_json
  auto_save: true
  colors:
    bbox: [0, 255, 0]
    text: [255, 255, 255]
  line_thickness: 2
```

## Project Structure

```
yolo_cam/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── config/
│   └── default_config.yaml # Default configuration
├── data/
│   ├── classes.txt        # Object class names
│   ├── images/            # Image storage
│   ├── annotations/       # Annotation files
│   └── training/          # Training datasets
├── models/                # YOLO model files
├── logs/                  # Application logs
├── MvImport/              # Camera SDK
│   ├── MvCameraControl_class.py
│   ├── CameraParams_header.py
│   └── ...
└── src/                   # Source code
    ├── __init__.py
    ├── config/            # Configuration management
    ├── logger/            # Logging utilities
    ├── utils/             # Common utilities
    ├── camera/            # Camera control
    ├── yolo/              # YOLO detection
    ├── annotation/        # Annotation tools
    └── gui/               # User interface
```

## Usage Examples

### Basic Camera Capture
```python
from src.camera import CameraController

# Initialize camera
config = {'trigger_mode': 'continuous', 'frame_rate': 30}
camera = CameraController(config)

# Connect and start capture
camera.connect(0)  # Connect to first camera
camera.start_grabbing()

# Get frames
frame_data = camera.get_latest_frame()
if frame_data:
    image = frame_data['image']
    # Process image...
```

### YOLO Detection
```python
from src.yolo import YOLODetector

# Initialize detector
config = {'model_path': 'models/yolov11n.pt', 'confidence_threshold': 0.5}
detector = YOLODetector(config)

# Load model and detect
detector.load_model()
detections = detector.detect(image)

# Draw results
result_image = detector.draw_detections(image, detections)
```

### Image Annotation
```python
from src.annotation import AnnotationTool

# Initialize annotation tool
config = {'format': 'yolo', 'auto_save': True}
annotator = AnnotationTool(config)

# Load image and add annotation
annotator.load_image('path/to/image.jpg')
annotator.add_annotation(x=320, y=240, width=100, height=80, class_name='person')

# Save annotations
annotator.save_annotations()
```

## Troubleshooting

### Common Issues

1. **Camera Not Detected**
   - Ensure camera drivers are installed
   - Check camera connection and power
   - Try refreshing the camera list

2. **YOLO Model Loading Failed**
   - Verify model file path and format
   - Check CUDA availability for GPU models
   - Ensure sufficient memory

3. **Annotation Not Saving**
   - Check file permissions in annotation directory
   - Verify annotation format settings
   - Ensure classes are loaded

4. **Training Errors**
   - Validate dataset structure and format
   - Check available disk space and memory
   - Verify training parameters

### Performance Optimization

1. **GPU Acceleration**
   - Install CUDA-compatible PyTorch
   - Set device to "cuda" in configuration
   - Monitor GPU memory usage

2. **Memory Management**
   - Adjust buffer sizes for your system
   - Enable memory optimization in settings
   - Close unnecessary applications

3. **Camera Performance**
   - Optimize exposure and gain settings
   - Reduce frame rate if needed
   - Use appropriate trigger mode

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the configuration documentation

## Acknowledgments

- Ultralytics for the YOLO implementation
- MvImport SDK for camera control
- PyQt6 for the user interface
- OpenCV for image processing
